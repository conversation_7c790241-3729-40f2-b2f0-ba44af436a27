ERROR - 2025-03-24 15:09:25 --> <PERSON>rror connecting to the database: mysqli_sql_exception: Unknown database '6xatz' in D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php:201
Stack trace:
#0 D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php(201): mysqli->real_connect('localhost', 'root', Object(SensitiveParameterValue), '6xatz', 3306, '', 0)
#1 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#2 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#3 D:\XAMPP\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 D:\XAMPP\htdocs\ci4\system\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#5 D:\XAMPP\htdocs\ci4\system\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
#6 D:\XAMPP\htdocs\ci4\app\Controllers\Article.php(10): CodeIgniter\BaseModel->findAll()
#7 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Article->index()
#8 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Article))
#9 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#10 D:\XAMPP\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#11 D:\XAMPP\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#12 D:\XAMPP\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#13 D:\XAMPP\htdocs\ci4\system\rewrite.php(44): require_once('D:\\XAMPP\\htdocs...')
#14 {main}

Next CodeIgniter\Database\Exceptions\DatabaseException: Unknown database '6xatz' in D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php:246
Stack trace:
#0 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#1 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#2 D:\XAMPP\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#3 D:\XAMPP\htdocs\ci4\system\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#4 D:\XAMPP\htdocs\ci4\system\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
#5 D:\XAMPP\htdocs\ci4\app\Controllers\Article.php(10): CodeIgniter\BaseModel->findAll()
#6 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Article->index()
#7 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Article))
#8 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 D:\XAMPP\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#10 D:\XAMPP\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#11 D:\XAMPP\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#12 D:\XAMPP\htdocs\ci4\system\rewrite.php(44): require_once('D:\\XAMPP\\htdocs...')
#13 {main}
CRITICAL - 2025-03-24 15:09:25 --> CodeIgniter\Database\Exceptions\DatabaseException: Unable to connect to the database.
Main connection [MySQLi]: Unknown database '6xatz'
[Method: GET, Route: article]
in SYSTEMPATH\Database\BaseConnection.php on line 467.
 1 SYSTEMPATH\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
 2 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`', [], false)
 3 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 4 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 5 APPPATH\Controllers\Article.php(10): CodeIgniter\BaseModel->findAll()
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Article->index()
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Article))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
12 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
