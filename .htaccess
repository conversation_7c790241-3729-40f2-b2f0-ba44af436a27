# Disable directory browsing
Options -Indexes

<IfModule mod_rewrite.c>
    Options +FollowSymlinks
    RewriteEngine On

    # ⛔ Skip rewrite jika path ke /vue/ (biar Vue jalan mandiri)
    RewriteCond %{REQUEST_URI} ^/vue/
    RewriteRule ^ - [L]

    # Redirect request yang bukan file atau folder ke index.php
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteRule ^([\s\S]*)$ index.php/$1 [L,NC,QSA]

    # Bawa header Authorization (penting buat API dll)
    RewriteCond %{HTTP:Authorization} .
    RewriteRule .* - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]
</IfModule>

<IfModule !mod_rewrite.c>
    ErrorDocument 404 index.php
</IfModule>
