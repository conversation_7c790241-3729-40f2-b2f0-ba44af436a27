body {
  font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: #eaeaea;
  font-weight: 400;
  line-height: 1.6;
}

/* Typography - Bold Text Support */
h1, h2, h3, h4, h5, h6 {
  font-weight: 700 !important;
}

.font-bold,
.fw-bold,
strong,
b {
  font-weight: 700 !important;
}

.font-semibold,
.fw-semibold {
  font-weight: 600 !important;
}

.font-medium,
.fw-medium {
  font-weight: 500 !important;
}

/* Labels and Important Text */
label {
  font-weight: 600 !important;
  color: #333;
  margin-bottom: 5px;
  display: block;
}

.table th {
  font-weight: 700 !important;
  background-color: #f8f9fa !important;
}

/* <PERSON><PERSON>m Isi Artikel */
.isi-column {
  max-width: 200px;
  word-wrap: break-word;
  white-space: pre-wrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-height: 60px;
  overflow-y: auto;
  font-size: 12px;
  line-height: 1.4;
}

.btn {
  font-weight: 500 !important;
}
#app {
  margin: 50px auto;
  width: 900px;
  background: #fff;
  padding: 20px;
  box-shadow: 0 0 10px rgba(0,0,0,0.2);
}
h1 {
  margin-bottom: 20px;
  font-weight: 700 !important;
  color: #2c3e50;
}

/* Text Content Styling - Support for Bold */
.article-content,
.content-text,
textarea,
input[type="text"] {
  font-family: inherit;
}

/* Make sure bold text works in content areas */
.article-content strong,
.article-content b,
.content-text strong,
.content-text b,
textarea strong,
textarea b {
  font-weight: 700 !important;
}

/* Table content bold support */
.table td strong,
.table td b {
  font-weight: 700 !important;
}

/* Modal content bold support */
.modal-body strong,
.modal-body b {
  font-weight: 700 !important;
}
#btn-tambah {
  margin-bottom: 15px;
  padding: 10px 20px;
  cursor: pointer;
  background-color: #3152d6;
  color: #ffffff;
  border: 1px solid #3152d6;
  font-weight: bold;
}
/* Table Container with Scroll */
.table-container {
  width: 100%;
  overflow-x: auto;
  margin-top: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  background: white;
}

table {
  width: 100%;
  min-width: 700px; /* Minimum width untuk scroll */
  border-collapse: collapse;
}
th {
  background-color: #5778ff;
  color: white;
  padding: 10px;
}
td {
  padding: 10px;
  border-bottom: 1px solid #ccc;
}
tr:nth-child(odd) {
  background-color: #f9f9f9;
}
.center-text {
  text-align: center;
}
td a {
  color: #3152d6;
  margin: 0 5px;
  text-decoration: none;
}

/* Modal */
.modal {
  position: fixed;
  z-index: 999;
  top: 0; left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0,0,0,0.5);
  display: flex;
  align-items: center;
  justify-content: center;
}
.modal-content {
  background-color: #fff;
  padding: 20px;
  width: 600px;
  position: relative;
  border-radius: 8px;
  box-shadow: 0 0 20px rgba(0,0,0,0.3);
}
.close {
  position: absolute;
  top: 10px; right: 10px;
  font-size: 24px;
  font-weight: bold;
  color: #aaa;
  cursor: pointer;
}
.close:hover {
  color: #000;
}
form input,
form textarea,
form select {
  width: 100%;
  padding: 8px;
  margin-bottom: 10px;
  box-sizing: border-box;
}
#btnSimpan {
  background-color: #3152d6;
  color: white;
  border: 1px solid #3152d6;
  padding: 10px 20px;
  margin-right: 10px;
  cursor: pointer;
}
form button {
  padding: 10px 20px;
  margin-top: 10px;
}

/* ===== IMAGE STYLES - SEPARATED BY CONTEXT ===== */

/* TABLE IMAGES ONLY - Tiny Icons */
table img,
table td img,
table tbody img,
table tr img,
table * img,
td img,
tbody img,
tr img,
.table-container img,
.table-container td img,
.table-container tbody img,
.table-container tr img,
img.table-image,
table .table-image,
table td .table-image,
tbody td .table-image,
tr td .table-image {
  max-width: 40px !important;
  max-height: 30px !important;
  width: auto !important;
  height: auto !important;
  min-width: 20px !important;
  min-height: 15px !important;
  object-fit: contain !important;
  border-radius: 3px !important;
  border: 1px solid #ccc !important;
  display: block !important;
  margin: 0 auto !important;
  overflow: hidden !important;
  box-sizing: border-box !important;
  background-color: #f8f9fa !important;
  cursor: pointer !important;
  transition: transform 0.2s ease !important;
}

/* Extra specific selectors for Vue.js rendered images IN TABLE */
tbody tr td img,
.table-container tbody tr td img {
  max-width: 40px !important;
  max-height: 30px !important;
  width: auto !important;
  height: auto !important;
  object-fit: contain !important;
}

/* Hover effect for table images */
table img:hover,
table td img:hover,
tbody img:hover,
td img:hover,
.table-image:hover {
  transform: scale(1.2) !important;
  border-color: #007bff !important;
  box-shadow: 0 2px 8px rgba(0,123,255,0.3) !important;
}

/* FORM PREVIEW IMAGES - Responsive for All Aspect Ratios */
.image-preview img,
.existing-image img,
.form-container img:not(.table-image),
.modal img:not(.table-image),
.modal-content img:not(.table-image) {
  max-width: 200px !important;
  max-height: 150px !important;
  width: auto !important;
  height: auto !important;
  object-fit: contain !important;
  border-radius: 6px !important;
  border: 1px solid #ddd !important;
  display: block !important;
  margin: 8px auto !important;
  background-color: #f8f9fa !important;
  padding: 2px !important;
}

/* MODAL FORM SPECIFIC IMAGE STYLING - Flexible for All Sizes */
.modal-body img,
#editModal img,
#addModal img {
  max-width: 180px !important;
  max-height: 140px !important;
  width: auto !important;
  height: auto !important;
  object-fit: contain !important;
  border-radius: 4px !important;
  border: 1px solid #ccc !important;
  display: block !important;
  margin: 5px auto !important;
  background-color: #ffffff !important;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
}

/* IMAGE CONTAINER FOR BETTER RESPONSIVE HANDLING */
.image-preview,
.existing-image,
.modal-body .form-group {
  text-align: center !important;
  overflow: hidden !important;
}

/* ENSURE IMAGES NEVER BREAK LAYOUT */
img {
  max-width: 100% !important;
  height: auto !important;
  vertical-align: middle !important;
}

/* Image Preview Container */
.image-preview {
  position: relative;
  display: inline-block;
  margin: 10px 0;
  border: 2px dashed #ddd;
  border-radius: 8px;
  padding: 15px;
  background: #f9f9f9;
  text-align: center;
}

/* Existing Image Container */
.existing-image {
  margin: 10px 0;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background: #f5f5f5;
  text-align: center;
}

.existing-image label {
  display: block;
  margin-bottom: 10px;
  font-weight: bold;
  color: #333;
}

/* Remove Image Button */
.remove-image {
  background: #ff4757;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 5px 10px;
  cursor: pointer;
  font-size: 12px;
  font-weight: bold;
  margin-top: 10px;
  transition: background 0.3s;
}

.remove-image:hover {
  background: #ff3742;
}

/* File Input Styling */
input[type="file"] {
  padding: 10px;
  border: 2px dashed #ddd;
  border-radius: 6px;
  background: #f9f9f9;
  width: 100%;
  cursor: pointer;
  font-size: 14px;
}

input[type="file"]:hover {
  border-color: #3152d6;
  background: #f0f4ff;
}

.file-info {
  color: #666;
  font-size: 12px;
  margin-top: 5px;
  display: block;
  font-style: italic;
}

.formatting-help {
  color: #666;
  font-size: 11px;
  margin-top: 3px;
  display: block;
  font-style: italic;
  background: #f8f9fa;
  padding: 3px 6px;
  border-radius: 3px;
  border-left: 3px solid #007bff;
}

/* No Image Placeholder */
.no-image {
  display: inline-block;
  padding: 5px 8px;
  background: #f0f0f0;
  color: #999;
  font-size: 10px;
  border-radius: 3px;
  border: 1px solid #ddd;
}

/* Image Preview dalam Form */
.image-preview {
  position: relative;
  display: inline-block;
  margin: 10px 0;
  border: 2px dashed #ddd;
  border-radius: 8px;
  padding: 10px;
  background: #f9f9f9;
}

.image-preview img {
  max-width: 200px !important;
  max-height: 200px !important;
  width: auto !important;
  height: auto !important;
  object-fit: contain !important;
  border-radius: 4px;
  display: block;
}

/* Existing Image Display */
.existing-image {
  margin: 10px 0;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background: #f5f5f5;
}

.existing-image img {
  max-width: 200px !important;
  max-height: 200px !important;
  width: auto !important;
  height: auto !important;
  object-fit: contain !important;
  border-radius: 4px;
  display: block;
  margin-bottom: 10px;
}

/* Remove Image Button */
.remove-image {
  background: #ff4757;
  color: white;
  border: none;
  border-radius: 50%;
  width: 25px;
  height: 25px;
  cursor: pointer;
  font-size: 16px;
  font-weight: bold;
  position: absolute;
  top: 5px;
  right: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.existing-image .remove-image {
  position: static;
  border-radius: 4px;
  width: auto;
  height: auto;
  padding: 5px 10px;
  font-size: 12px;
}

.remove-image:hover {
  background: #ff3742;
}

/* File Input Styling */
input[type="file"] {
  padding: 8px;
  border: 2px dashed #ddd;
  border-radius: 4px;
  background: #f9f9f9;
  width: 100%;
  cursor: pointer;
}

input[type="file"]:hover {
  border-color: #3152d6;
  background: #f0f4ff;
}

.file-info {
  color: #666;
  font-size: 11px;
  margin-top: 5px;
  display: block;
}

/* No Image Placeholder */
.no-image {
  display: inline-block;
  padding: 5px 8px;
  background: #f0f0f0;
  color: #999;
  font-size: 10px;
  border-radius: 3px;
  border: 1px solid #ddd;
}

/* General Image Constraints - Fallback */
img {
  max-width: 100%;
  height: auto;
}

/* Specific overrides for any rogue images */
.container img:not(.table-image):not(.image-preview img):not(.existing-image img) {
  max-width: 300px !important;
  max-height: 300px !important;
  object-fit: contain !important;
}

.no-image {
  color: #999;
  font-style: italic;
  font-size: 12px;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

.file-info {
  display: block;
  color: #666;
  font-size: 12px;
  margin-top: 5px;
}

.image-preview {
  position: relative;
  display: inline-block;
  margin: 10px 0;
}

.image-preview img {
  max-width: 80px !important;
  max-height: 80px !important;
  border-radius: 4px;
  border: 2px solid #ddd;
}

.existing-image {
  margin: 10px 0;
}

.existing-image label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

.existing-image img {
  max-width: 200px;
  max-height: 200px;
  border-radius: 4px;
  border: 2px solid #ddd;
  margin-bottom: 10px;
}

.remove-image {
  position: absolute;
  top: -10px;
  right: -10px;
  background: #ff4444;
  color: white;
  border: none;
  border-radius: 50%;
  width: 25px;
  height: 25px;
  cursor: pointer;
  font-size: 16px;
  line-height: 1;
}

.existing-image .remove-image {
  position: static;
  border-radius: 4px;
  width: auto;
  height: auto;
  padding: 5px 10px;
  font-size: 12px;
}

.remove-image:hover {
  background: #cc0000;
}

#btnSimpan:disabled {
  background-color: #ccc;
  border-color: #ccc;
  cursor: not-allowed;
}

/* ===========================
   📝 RICH TEXT EDITOR STYLES
   =========================== */

.editor-container {
  margin: 15px 0;
}

.editor-container label {
  display: block;
  margin-bottom: 8px;
  font-weight: bold;
  color: #333;
}

.editor-toolbar {
  display: flex;
  gap: 5px;
  margin-bottom: 10px;
  padding: 8px;
  background: #f8f9fa;
  border: 1px solid #ddd;
  border-radius: 5px 5px 0 0;
  flex-wrap: wrap;
}

.editor-toolbar button {
  padding: 6px 10px;
  border: 1px solid #ccc;
  background: white;
  border-radius: 3px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
  min-width: 35px;
}

.editor-toolbar button:hover {
  background: #e9ecef;
  border-color: #007bff;
}

.editor-toolbar button:active {
  background: #007bff;
  color: white;
}

.editor-input {
  border: 1px solid #ddd;
  border-top: none;
  border-radius: 0 0 5px 5px;
  overflow: hidden;
}

.editor-input textarea {
  width: 100%;
  height: 250px;
  border: none;
  padding: 15px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  font-size: 14px;
  line-height: 1.6;
  resize: vertical;
  outline: none;
  background: #fafafa;
}

.formatting-help {
  display: block;
  margin-top: 8px;
  color: #666;
  font-size: 12px;
  line-height: 1.4;
}

/* ===========================
   🎯 RESPONSIVE DESIGN
   =========================== */
@media (max-width: 768px) {
  .container {
    padding: 10px;
  }

  table {
    font-size: 12px;
  }

  .modal-content {
    width: 95%;
    margin: 5% auto;
    padding: 15px;
  }

  .btn {
    padding: 8px 12px;
    font-size: 12px;
  }

  /* Mobile Editor */
  .editor-input textarea {
    height: 200px;
    font-size: 13px;
  }

  .editor-toolbar {
    gap: 3px;
  }

  .editor-toolbar button {
    padding: 4px 6px;
    font-size: 11px;
    min-width: 30px;
  }
}

/* Enhanced Modal Styling */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease;
}

.modal-content-enhanced {
  background: white;
  border-radius: 20px;
  max-width: 700px;
  width: 95%;
  max-height: 95vh;
  overflow-y: auto;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
  animation: slideUp 0.3s ease;
}

.modal-header-enhanced {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  padding: 1.5rem 2rem;
  border-radius: 20px 20px 0 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-title-enhanced {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
}

.btn-close-enhanced {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-close-enhanced:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.modal-form {
  padding: 2rem;
}

/* Enhanced Form Styling */
.form-group-enhanced {
  margin-bottom: 2rem;
}

.form-label-enhanced {
  display: block;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 0.75rem;
  font-size: 1rem;
}

.form-input-enhanced, .form-select-enhanced {
  width: 100%;
  padding: 1rem;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: #f7fafc;
}

.form-input-enhanced:focus, .form-select-enhanced:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
  background: white;
}

.form-textarea-enhanced {
  width: 100%;
  padding: 1rem;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: #f7fafc;
  min-height: 150px;
  resize: vertical;
  font-family: inherit;
  line-height: 1.6;
}

.form-textarea-enhanced:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
  background: white;
}

/* Enhanced Toolbar */
.editor-toolbar-enhanced {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
  padding: 1rem;
  background: #f7fafc;
  border-radius: 12px;
  border: 2px solid #e2e8f0;
}

.toolbar-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: white;
  border: 1px solid #e2e8f0;
  padding: 0.75rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.875rem;
  font-weight: 500;
  color: #4a5568;
}

.toolbar-btn:hover {
  background: #667eea;
  color: white;
  border-color: #667eea;
  transform: translateY(-1px);
}

.editor-help {
  margin-top: 0.5rem;
  color: #718096;
  font-size: 0.875rem;
}

/* File Upload Styling */
.file-upload-wrapper {
  position: relative;
}

.file-input-hidden {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
}

.file-upload-btn {
  display: inline-flex;
  align-items: center;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  padding: 1rem 1.5rem;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
  border: none;
}

.file-upload-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
  color: white;
}

.file-info {
  margin-top: 0.75rem;
  color: #718096;
  font-size: 0.875rem;
}

/* Image Preview Enhanced */
.image-preview-wrapper {
  position: relative;
  display: inline-block;
  border-radius: 12px;
  overflow: hidden;
  border: 2px solid #e2e8f0;
}

.image-preview-enhanced {
  max-width: 200px;
  max-height: 200px;
  width: auto;
  height: auto;
  display: block;
}

.remove-image-btn {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: rgba(255, 107, 107, 0.9);
  color: white;
  border: none;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.875rem;
}

.remove-image-btn:hover {
  background: #ff6b6b;
  transform: scale(1.1);
}

/* Form Actions Enhanced */
.form-actions-enhanced {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  padding-top: 2rem;
  border-top: 1px solid #e2e8f0;
  margin-top: 2rem;
}

.btn-submit-enhanced {
  background: linear-gradient(135deg, #56ab2f, #a8e6cf);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-submit-enhanced:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(86, 171, 47, 0.3);
}

.btn-submit-enhanced:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-cancel-enhanced {
  background: #f7fafc;
  color: #4a5568;
  border: 2px solid #e2e8f0;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-cancel-enhanced:hover {
  background: #edf2f7;
  border-color: #cbd5e0;
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
