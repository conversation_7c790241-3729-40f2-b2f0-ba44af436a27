DEBUG - 2025-06-20 03:08:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-20 03:08:14 --> Error connecting to the database: mysqli_sql_exception: No connection could be made because the target machine actively refused it in D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php:201
Stack trace:
#0 D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php(201): mysqli->real_connect('localhost', 'root', Object(SensitiveParameterValue), 'sukses', 3306, '', 0)
#1 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#2 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#3 D:\XAMPP\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 D:\XAMPP\htdocs\ci4\system\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#5 D:\XAMPP\htdocs\ci4\system\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
#6 D:\XAMPP\htdocs\ci4\app\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
#7 D:\XAMPP\htdocs\ci4\system\View\Cell.php(307): App\Cells\ArticleLatest->render(NULL)
#8 D:\XAMPP\htdocs\ci4\system\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', Array, 'App\\Cells\\Artic...')
#9 D:\XAMPP\htdocs\ci4\system\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\Artic...', Array, 0, 'AppCellsArticle...')
#10 D:\XAMPP\htdocs\ci4\app\Views\template\footer.php(4): view_cell('App\\Cells\\Artic...', Array)
#11 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#12 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#13 D:\XAMPP\htdocs\ci4\system\View\View.php(475): CodeIgniter\View\View->render('template/footer', NULL, true)
#14 D:\XAMPP\htdocs\ci4\app\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
#15 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#16 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#17 D:\XAMPP\htdocs\ci4\system\Common.php(1173): CodeIgniter\View\View->render('home', Array, true)
#18 D:\XAMPP\htdocs\ci4\app\Controllers\Page.php(7): view('home', Array)
#19 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#20 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#21 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#22 D:\XAMPP\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#23 D:\XAMPP\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#24 D:\XAMPP\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#25 D:\XAMPP\htdocs\ci4\system\rewrite.php(44): require_once('D:\\XAMPP\\htdocs...')
#26 {main}

Next CodeIgniter\Database\Exceptions\DatabaseException: No connection could be made because the target machine actively refused it in D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php:246
Stack trace:
#0 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#1 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#2 D:\XAMPP\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#3 D:\XAMPP\htdocs\ci4\system\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#4 D:\XAMPP\htdocs\ci4\system\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
#5 D:\XAMPP\htdocs\ci4\app\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
#6 D:\XAMPP\htdocs\ci4\system\View\Cell.php(307): App\Cells\ArticleLatest->render(NULL)
#7 D:\XAMPP\htdocs\ci4\system\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', Array, 'App\\Cells\\Artic...')
#8 D:\XAMPP\htdocs\ci4\system\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\Artic...', Array, 0, 'AppCellsArticle...')
#9 D:\XAMPP\htdocs\ci4\app\Views\template\footer.php(4): view_cell('App\\Cells\\Artic...', Array)
#10 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#11 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#12 D:\XAMPP\htdocs\ci4\system\View\View.php(475): CodeIgniter\View\View->render('template/footer', NULL, true)
#13 D:\XAMPP\htdocs\ci4\app\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
#14 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#15 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#16 D:\XAMPP\htdocs\ci4\system\Common.php(1173): CodeIgniter\View\View->render('home', Array, true)
#17 D:\XAMPP\htdocs\ci4\app\Controllers\Page.php(7): view('home', Array)
#18 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#19 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#20 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#21 D:\XAMPP\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#22 D:\XAMPP\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#23 D:\XAMPP\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#24 D:\XAMPP\htdocs\ci4\system\rewrite.php(44): require_once('D:\\XAMPP\\htdocs...')
#25 {main}
CRITICAL - 2025-06-20 03:08:14 --> CodeIgniter\Database\Exceptions\DatabaseException: Unable to connect to the database.
Main connection [MySQLi]: No connection could be made because the target machine actively refused it
[Method: GET, Route: /]
in SYSTEMPATH\Database\BaseConnection.php on line 467.
 1 SYSTEMPATH\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
 2 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 3 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 4 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 5 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 6 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 7 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 8 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
 9 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
10 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
11 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
12 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
13 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
14 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
15 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
16 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
17 APPPATH\Controllers\Page.php(7): view('home', [...])
18 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
19 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
20 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
21 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
22 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
23 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
24 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
DEBUG - 2025-06-20 03:08:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-20 03:08:48 --> Error connecting to the database: mysqli_sql_exception: No connection could be made because the target machine actively refused it in D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php:201
Stack trace:
#0 D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php(201): mysqli->real_connect('localhost', 'root', Object(SensitiveParameterValue), 'sukses', 3306, '', 0)
#1 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#2 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#3 D:\XAMPP\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 D:\XAMPP\htdocs\ci4\system\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#5 D:\XAMPP\htdocs\ci4\system\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
#6 D:\XAMPP\htdocs\ci4\app\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
#7 D:\XAMPP\htdocs\ci4\system\View\Cell.php(307): App\Cells\ArticleLatest->render(NULL)
#8 D:\XAMPP\htdocs\ci4\system\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', Array, 'App\\Cells\\Artic...')
#9 D:\XAMPP\htdocs\ci4\system\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\Artic...', Array, 0, 'AppCellsArticle...')
#10 D:\XAMPP\htdocs\ci4\app\Views\template\footer.php(4): view_cell('App\\Cells\\Artic...', Array)
#11 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#12 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#13 D:\XAMPP\htdocs\ci4\system\View\View.php(475): CodeIgniter\View\View->render('template/footer', NULL, true)
#14 D:\XAMPP\htdocs\ci4\app\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
#15 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#16 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#17 D:\XAMPP\htdocs\ci4\system\Common.php(1173): CodeIgniter\View\View->render('home', Array, true)
#18 D:\XAMPP\htdocs\ci4\app\Controllers\Page.php(7): view('home', Array)
#19 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#20 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#21 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#22 D:\XAMPP\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#23 D:\XAMPP\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#24 D:\XAMPP\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#25 D:\XAMPP\htdocs\ci4\system\rewrite.php(44): require_once('D:\\XAMPP\\htdocs...')
#26 {main}

Next CodeIgniter\Database\Exceptions\DatabaseException: No connection could be made because the target machine actively refused it in D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php:246
Stack trace:
#0 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#1 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#2 D:\XAMPP\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#3 D:\XAMPP\htdocs\ci4\system\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#4 D:\XAMPP\htdocs\ci4\system\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
#5 D:\XAMPP\htdocs\ci4\app\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
#6 D:\XAMPP\htdocs\ci4\system\View\Cell.php(307): App\Cells\ArticleLatest->render(NULL)
#7 D:\XAMPP\htdocs\ci4\system\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', Array, 'App\\Cells\\Artic...')
#8 D:\XAMPP\htdocs\ci4\system\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\Artic...', Array, 0, 'AppCellsArticle...')
#9 D:\XAMPP\htdocs\ci4\app\Views\template\footer.php(4): view_cell('App\\Cells\\Artic...', Array)
#10 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#11 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#12 D:\XAMPP\htdocs\ci4\system\View\View.php(475): CodeIgniter\View\View->render('template/footer', NULL, true)
#13 D:\XAMPP\htdocs\ci4\app\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
#14 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#15 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#16 D:\XAMPP\htdocs\ci4\system\Common.php(1173): CodeIgniter\View\View->render('home', Array, true)
#17 D:\XAMPP\htdocs\ci4\app\Controllers\Page.php(7): view('home', Array)
#18 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#19 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#20 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#21 D:\XAMPP\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#22 D:\XAMPP\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#23 D:\XAMPP\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#24 D:\XAMPP\htdocs\ci4\system\rewrite.php(44): require_once('D:\\XAMPP\\htdocs...')
#25 {main}
CRITICAL - 2025-06-20 03:08:48 --> CodeIgniter\Database\Exceptions\DatabaseException: Unable to connect to the database.
Main connection [MySQLi]: No connection could be made because the target machine actively refused it
[Method: GET, Route: /]
in SYSTEMPATH\Database\BaseConnection.php on line 467.
 1 SYSTEMPATH\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
 2 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 3 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 4 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 5 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 6 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 7 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 8 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
 9 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
10 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
11 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
12 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
13 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
14 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
15 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
16 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
17 APPPATH\Controllers\Page.php(7): view('home', [...])
18 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
19 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
20 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
21 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
22 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
23 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
24 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
DEBUG - 2025-06-20 03:10:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-20 03:10:19 --> Error connecting to the database: mysqli_sql_exception: No connection could be made because the target machine actively refused it in D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php:201
Stack trace:
#0 D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php(201): mysqli->real_connect('localhost', 'root', Object(SensitiveParameterValue), 'sukses', 3306, '', 0)
#1 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#2 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#3 D:\XAMPP\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 D:\XAMPP\htdocs\ci4\system\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#5 D:\XAMPP\htdocs\ci4\system\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
#6 D:\XAMPP\htdocs\ci4\app\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
#7 D:\XAMPP\htdocs\ci4\system\View\Cell.php(307): App\Cells\ArticleLatest->render(NULL)
#8 D:\XAMPP\htdocs\ci4\system\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', Array, 'App\\Cells\\Artic...')
#9 D:\XAMPP\htdocs\ci4\system\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\Artic...', Array, 0, 'AppCellsArticle...')
#10 D:\XAMPP\htdocs\ci4\app\Views\template\footer.php(4): view_cell('App\\Cells\\Artic...', Array)
#11 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#12 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#13 D:\XAMPP\htdocs\ci4\system\View\View.php(475): CodeIgniter\View\View->render('template/footer', NULL, true)
#14 D:\XAMPP\htdocs\ci4\app\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
#15 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#16 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#17 D:\XAMPP\htdocs\ci4\system\Common.php(1173): CodeIgniter\View\View->render('home', Array, true)
#18 D:\XAMPP\htdocs\ci4\app\Controllers\Page.php(7): view('home', Array)
#19 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#20 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#21 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#22 D:\XAMPP\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#23 D:\XAMPP\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#24 D:\XAMPP\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#25 D:\XAMPP\htdocs\ci4\system\rewrite.php(44): require_once('D:\\XAMPP\\htdocs...')
#26 {main}

Next CodeIgniter\Database\Exceptions\DatabaseException: No connection could be made because the target machine actively refused it in D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php:246
Stack trace:
#0 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#1 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#2 D:\XAMPP\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#3 D:\XAMPP\htdocs\ci4\system\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#4 D:\XAMPP\htdocs\ci4\system\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
#5 D:\XAMPP\htdocs\ci4\app\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
#6 D:\XAMPP\htdocs\ci4\system\View\Cell.php(307): App\Cells\ArticleLatest->render(NULL)
#7 D:\XAMPP\htdocs\ci4\system\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', Array, 'App\\Cells\\Artic...')
#8 D:\XAMPP\htdocs\ci4\system\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\Artic...', Array, 0, 'AppCellsArticle...')
#9 D:\XAMPP\htdocs\ci4\app\Views\template\footer.php(4): view_cell('App\\Cells\\Artic...', Array)
#10 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#11 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#12 D:\XAMPP\htdocs\ci4\system\View\View.php(475): CodeIgniter\View\View->render('template/footer', NULL, true)
#13 D:\XAMPP\htdocs\ci4\app\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
#14 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#15 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#16 D:\XAMPP\htdocs\ci4\system\Common.php(1173): CodeIgniter\View\View->render('home', Array, true)
#17 D:\XAMPP\htdocs\ci4\app\Controllers\Page.php(7): view('home', Array)
#18 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#19 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#20 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#21 D:\XAMPP\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#22 D:\XAMPP\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#23 D:\XAMPP\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#24 D:\XAMPP\htdocs\ci4\system\rewrite.php(44): require_once('D:\\XAMPP\\htdocs...')
#25 {main}
CRITICAL - 2025-06-20 03:10:19 --> CodeIgniter\Database\Exceptions\DatabaseException: Unable to connect to the database.
Main connection [MySQLi]: No connection could be made because the target machine actively refused it
[Method: GET, Route: /]
in SYSTEMPATH\Database\BaseConnection.php on line 467.
 1 SYSTEMPATH\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
 2 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 3 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 4 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 5 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 6 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 7 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 8 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
 9 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
10 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
11 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
12 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
13 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
14 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
15 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
16 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
17 APPPATH\Controllers\Page.php(7): view('home', [...])
18 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
19 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
20 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
21 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
22 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
23 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
24 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
DEBUG - 2025-06-20 03:34:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-20 03:36:40 --> ErrorException: Maximum execution time of 60 seconds exceeded
[Method: GET, Route: /]
in SYSTEMPATH\Debug\Exceptions.php on line 208.
 1 [internal function]: CodeIgniter\Debug\Exceptions->shutdownHandler()
DEBUG - 2025-06-20 03:36:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-20 03:36:45 --> Error connecting to the database: mysqli_sql_exception: No connection could be made because the target machine actively refused it in D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php:201
Stack trace:
#0 D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php(201): mysqli->real_connect('localhost', 'root', Object(SensitiveParameterValue), 'sukses', 3306, '', 0)
#1 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#2 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#3 D:\XAMPP\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 D:\XAMPP\htdocs\ci4\system\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#5 D:\XAMPP\htdocs\ci4\system\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
#6 D:\XAMPP\htdocs\ci4\app\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
#7 D:\XAMPP\htdocs\ci4\system\View\Cell.php(307): App\Cells\ArticleLatest->render(NULL)
#8 D:\XAMPP\htdocs\ci4\system\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', Array, 'App\\Cells\\Artic...')
#9 D:\XAMPP\htdocs\ci4\system\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\Artic...', Array, 0, 'AppCellsArticle...')
#10 D:\XAMPP\htdocs\ci4\app\Views\template\footer.php(4): view_cell('App\\Cells\\Artic...', Array)
#11 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#12 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#13 D:\XAMPP\htdocs\ci4\system\View\View.php(475): CodeIgniter\View\View->render('template/footer', NULL, true)
#14 D:\XAMPP\htdocs\ci4\app\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
#15 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#16 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#17 D:\XAMPP\htdocs\ci4\system\Common.php(1173): CodeIgniter\View\View->render('home', Array, true)
#18 D:\XAMPP\htdocs\ci4\app\Controllers\Page.php(7): view('home', Array)
#19 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#20 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#21 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#22 D:\XAMPP\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#23 D:\XAMPP\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#24 D:\XAMPP\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#25 D:\XAMPP\htdocs\ci4\system\rewrite.php(44): require_once('D:\\XAMPP\\htdocs...')
#26 {main}

Next CodeIgniter\Database\Exceptions\DatabaseException: No connection could be made because the target machine actively refused it in D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php:246
Stack trace:
#0 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#1 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#2 D:\XAMPP\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#3 D:\XAMPP\htdocs\ci4\system\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#4 D:\XAMPP\htdocs\ci4\system\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
#5 D:\XAMPP\htdocs\ci4\app\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
#6 D:\XAMPP\htdocs\ci4\system\View\Cell.php(307): App\Cells\ArticleLatest->render(NULL)
#7 D:\XAMPP\htdocs\ci4\system\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', Array, 'App\\Cells\\Artic...')
#8 D:\XAMPP\htdocs\ci4\system\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\Artic...', Array, 0, 'AppCellsArticle...')
#9 D:\XAMPP\htdocs\ci4\app\Views\template\footer.php(4): view_cell('App\\Cells\\Artic...', Array)
#10 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#11 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#12 D:\XAMPP\htdocs\ci4\system\View\View.php(475): CodeIgniter\View\View->render('template/footer', NULL, true)
#13 D:\XAMPP\htdocs\ci4\app\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
#14 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#15 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#16 D:\XAMPP\htdocs\ci4\system\Common.php(1173): CodeIgniter\View\View->render('home', Array, true)
#17 D:\XAMPP\htdocs\ci4\app\Controllers\Page.php(7): view('home', Array)
#18 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#19 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#20 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#21 D:\XAMPP\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#22 D:\XAMPP\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#23 D:\XAMPP\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#24 D:\XAMPP\htdocs\ci4\system\rewrite.php(44): require_once('D:\\XAMPP\\htdocs...')
#25 {main}
CRITICAL - 2025-06-20 03:36:45 --> CodeIgniter\Database\Exceptions\DatabaseException: Unable to connect to the database.
Main connection [MySQLi]: No connection could be made because the target machine actively refused it
[Method: GET, Route: /]
in SYSTEMPATH\Database\BaseConnection.php on line 467.
 1 SYSTEMPATH\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
 2 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 3 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 4 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 5 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 6 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 7 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 8 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
 9 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
10 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
11 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
12 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
13 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
14 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
15 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
16 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
17 APPPATH\Controllers\Page.php(7): view('home', [...])
18 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
19 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
20 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
21 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
22 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
23 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
24 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
DEBUG - 2025-06-20 09:04:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-20 09:04:47 --> Error connecting to the database: mysqli_sql_exception: No connection could be made because the target machine actively refused it in D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php:201
Stack trace:
#0 D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php(201): mysqli->real_connect('localhost', 'root', Object(SensitiveParameterValue), 'sukses', 3306, '', 0)
#1 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#2 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#3 D:\XAMPP\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 D:\XAMPP\htdocs\ci4\system\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#5 D:\XAMPP\htdocs\ci4\system\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
#6 D:\XAMPP\htdocs\ci4\app\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
#7 D:\XAMPP\htdocs\ci4\system\View\Cell.php(307): App\Cells\ArticleLatest->render(NULL)
#8 D:\XAMPP\htdocs\ci4\system\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', Array, 'App\\Cells\\Artic...')
#9 D:\XAMPP\htdocs\ci4\system\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\Artic...', Array, 0, 'AppCellsArticle...')
#10 D:\XAMPP\htdocs\ci4\app\Views\template\footer.php(4): view_cell('App\\Cells\\Artic...', Array)
#11 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#12 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#13 D:\XAMPP\htdocs\ci4\system\View\View.php(475): CodeIgniter\View\View->render('template/footer', NULL, true)
#14 D:\XAMPP\htdocs\ci4\app\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
#15 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#16 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#17 D:\XAMPP\htdocs\ci4\system\Common.php(1173): CodeIgniter\View\View->render('home', Array, true)
#18 D:\XAMPP\htdocs\ci4\app\Controllers\Page.php(7): view('home', Array)
#19 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#20 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#21 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#22 D:\XAMPP\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#23 D:\XAMPP\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#24 D:\XAMPP\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#25 D:\XAMPP\htdocs\ci4\system\rewrite.php(44): require_once('D:\\XAMPP\\htdocs...')
#26 {main}

Next CodeIgniter\Database\Exceptions\DatabaseException: No connection could be made because the target machine actively refused it in D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php:246
Stack trace:
#0 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#1 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#2 D:\XAMPP\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#3 D:\XAMPP\htdocs\ci4\system\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#4 D:\XAMPP\htdocs\ci4\system\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
#5 D:\XAMPP\htdocs\ci4\app\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
#6 D:\XAMPP\htdocs\ci4\system\View\Cell.php(307): App\Cells\ArticleLatest->render(NULL)
#7 D:\XAMPP\htdocs\ci4\system\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', Array, 'App\\Cells\\Artic...')
#8 D:\XAMPP\htdocs\ci4\system\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\Artic...', Array, 0, 'AppCellsArticle...')
#9 D:\XAMPP\htdocs\ci4\app\Views\template\footer.php(4): view_cell('App\\Cells\\Artic...', Array)
#10 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#11 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#12 D:\XAMPP\htdocs\ci4\system\View\View.php(475): CodeIgniter\View\View->render('template/footer', NULL, true)
#13 D:\XAMPP\htdocs\ci4\app\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
#14 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#15 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#16 D:\XAMPP\htdocs\ci4\system\Common.php(1173): CodeIgniter\View\View->render('home', Array, true)
#17 D:\XAMPP\htdocs\ci4\app\Controllers\Page.php(7): view('home', Array)
#18 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#19 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#20 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#21 D:\XAMPP\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#22 D:\XAMPP\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#23 D:\XAMPP\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#24 D:\XAMPP\htdocs\ci4\system\rewrite.php(44): require_once('D:\\XAMPP\\htdocs...')
#25 {main}
CRITICAL - 2025-06-20 09:04:47 --> CodeIgniter\Database\Exceptions\DatabaseException: Unable to connect to the database.
Main connection [MySQLi]: No connection could be made because the target machine actively refused it
[Method: GET, Route: /]
in SYSTEMPATH\Database\BaseConnection.php on line 467.
 1 SYSTEMPATH\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
 2 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 3 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 4 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 5 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 6 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 7 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 8 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
 9 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
10 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
11 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
12 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
13 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
14 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
15 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
16 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
17 APPPATH\Controllers\Page.php(7): view('home', [...])
18 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
19 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
20 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
21 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
22 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
23 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
24 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
DEBUG - 2025-06-20 09:05:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-20 09:05:09 --> Error connecting to the database: mysqli_sql_exception: Unknown database 'sukses' in D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php:201
Stack trace:
#0 D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php(201): mysqli->real_connect('localhost', 'root', Object(SensitiveParameterValue), 'sukses', 3306, '', 0)
#1 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#2 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#3 D:\XAMPP\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 D:\XAMPP\htdocs\ci4\system\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#5 D:\XAMPP\htdocs\ci4\system\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
#6 D:\XAMPP\htdocs\ci4\app\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
#7 D:\XAMPP\htdocs\ci4\system\View\Cell.php(307): App\Cells\ArticleLatest->render(NULL)
#8 D:\XAMPP\htdocs\ci4\system\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', Array, 'App\\Cells\\Artic...')
#9 D:\XAMPP\htdocs\ci4\system\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\Artic...', Array, 0, 'AppCellsArticle...')
#10 D:\XAMPP\htdocs\ci4\app\Views\template\footer.php(4): view_cell('App\\Cells\\Artic...', Array)
#11 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#12 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#13 D:\XAMPP\htdocs\ci4\system\View\View.php(475): CodeIgniter\View\View->render('template/footer', NULL, true)
#14 D:\XAMPP\htdocs\ci4\app\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
#15 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#16 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#17 D:\XAMPP\htdocs\ci4\system\Common.php(1173): CodeIgniter\View\View->render('home', Array, true)
#18 D:\XAMPP\htdocs\ci4\app\Controllers\Page.php(7): view('home', Array)
#19 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#20 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#21 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#22 D:\XAMPP\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#23 D:\XAMPP\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#24 D:\XAMPP\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#25 D:\XAMPP\htdocs\ci4\system\rewrite.php(44): require_once('D:\\XAMPP\\htdocs...')
#26 {main}

Next CodeIgniter\Database\Exceptions\DatabaseException: Unknown database 'sukses' in D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php:246
Stack trace:
#0 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#1 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#2 D:\XAMPP\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#3 D:\XAMPP\htdocs\ci4\system\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#4 D:\XAMPP\htdocs\ci4\system\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
#5 D:\XAMPP\htdocs\ci4\app\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
#6 D:\XAMPP\htdocs\ci4\system\View\Cell.php(307): App\Cells\ArticleLatest->render(NULL)
#7 D:\XAMPP\htdocs\ci4\system\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', Array, 'App\\Cells\\Artic...')
#8 D:\XAMPP\htdocs\ci4\system\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\Artic...', Array, 0, 'AppCellsArticle...')
#9 D:\XAMPP\htdocs\ci4\app\Views\template\footer.php(4): view_cell('App\\Cells\\Artic...', Array)
#10 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#11 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#12 D:\XAMPP\htdocs\ci4\system\View\View.php(475): CodeIgniter\View\View->render('template/footer', NULL, true)
#13 D:\XAMPP\htdocs\ci4\app\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
#14 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#15 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#16 D:\XAMPP\htdocs\ci4\system\Common.php(1173): CodeIgniter\View\View->render('home', Array, true)
#17 D:\XAMPP\htdocs\ci4\app\Controllers\Page.php(7): view('home', Array)
#18 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#19 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#20 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#21 D:\XAMPP\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#22 D:\XAMPP\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#23 D:\XAMPP\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#24 D:\XAMPP\htdocs\ci4\system\rewrite.php(44): require_once('D:\\XAMPP\\htdocs...')
#25 {main}
CRITICAL - 2025-06-20 09:05:09 --> CodeIgniter\Database\Exceptions\DatabaseException: Unable to connect to the database.
Main connection [MySQLi]: Unknown database 'sukses'
[Method: GET, Route: /]
in SYSTEMPATH\Database\BaseConnection.php on line 467.
 1 SYSTEMPATH\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
 2 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 3 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 4 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 5 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 6 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 7 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 8 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
 9 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
10 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
11 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
12 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
13 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
14 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
15 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
16 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
17 APPPATH\Controllers\Page.php(7): view('home', [...])
18 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
19 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
20 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
21 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
22 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
23 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
24 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
DEBUG - 2025-06-20 09:05:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-20 09:05:12 --> Error connecting to the database: mysqli_sql_exception: Unknown database 'sukses' in D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php:201
Stack trace:
#0 D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php(201): mysqli->real_connect('localhost', 'root', Object(SensitiveParameterValue), 'sukses', 3306, '', 0)
#1 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#2 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#3 D:\XAMPP\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 D:\XAMPP\htdocs\ci4\system\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#5 D:\XAMPP\htdocs\ci4\system\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
#6 D:\XAMPP\htdocs\ci4\app\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
#7 D:\XAMPP\htdocs\ci4\system\View\Cell.php(307): App\Cells\ArticleLatest->render(NULL)
#8 D:\XAMPP\htdocs\ci4\system\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', Array, 'App\\Cells\\Artic...')
#9 D:\XAMPP\htdocs\ci4\system\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\Artic...', Array, 0, 'AppCellsArticle...')
#10 D:\XAMPP\htdocs\ci4\app\Views\template\footer.php(4): view_cell('App\\Cells\\Artic...', Array)
#11 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#12 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#13 D:\XAMPP\htdocs\ci4\system\View\View.php(475): CodeIgniter\View\View->render('template/footer', NULL, true)
#14 D:\XAMPP\htdocs\ci4\app\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
#15 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#16 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#17 D:\XAMPP\htdocs\ci4\system\Common.php(1173): CodeIgniter\View\View->render('home', Array, true)
#18 D:\XAMPP\htdocs\ci4\app\Controllers\Page.php(7): view('home', Array)
#19 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#20 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#21 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#22 D:\XAMPP\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#23 D:\XAMPP\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#24 D:\XAMPP\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#25 D:\XAMPP\htdocs\ci4\system\rewrite.php(44): require_once('D:\\XAMPP\\htdocs...')
#26 {main}

Next CodeIgniter\Database\Exceptions\DatabaseException: Unknown database 'sukses' in D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php:246
Stack trace:
#0 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#1 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#2 D:\XAMPP\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#3 D:\XAMPP\htdocs\ci4\system\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#4 D:\XAMPP\htdocs\ci4\system\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
#5 D:\XAMPP\htdocs\ci4\app\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
#6 D:\XAMPP\htdocs\ci4\system\View\Cell.php(307): App\Cells\ArticleLatest->render(NULL)
#7 D:\XAMPP\htdocs\ci4\system\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', Array, 'App\\Cells\\Artic...')
#8 D:\XAMPP\htdocs\ci4\system\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\Artic...', Array, 0, 'AppCellsArticle...')
#9 D:\XAMPP\htdocs\ci4\app\Views\template\footer.php(4): view_cell('App\\Cells\\Artic...', Array)
#10 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#11 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#12 D:\XAMPP\htdocs\ci4\system\View\View.php(475): CodeIgniter\View\View->render('template/footer', NULL, true)
#13 D:\XAMPP\htdocs\ci4\app\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
#14 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#15 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#16 D:\XAMPP\htdocs\ci4\system\Common.php(1173): CodeIgniter\View\View->render('home', Array, true)
#17 D:\XAMPP\htdocs\ci4\app\Controllers\Page.php(7): view('home', Array)
#18 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#19 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#20 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#21 D:\XAMPP\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#22 D:\XAMPP\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#23 D:\XAMPP\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#24 D:\XAMPP\htdocs\ci4\system\rewrite.php(44): require_once('D:\\XAMPP\\htdocs...')
#25 {main}
CRITICAL - 2025-06-20 09:05:12 --> CodeIgniter\Database\Exceptions\DatabaseException: Unable to connect to the database.
Main connection [MySQLi]: Unknown database 'sukses'
[Method: GET, Route: /]
in SYSTEMPATH\Database\BaseConnection.php on line 467.
 1 SYSTEMPATH\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
 2 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 3 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 4 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 5 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 6 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 7 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 8 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
 9 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
10 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
11 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
12 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
13 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
14 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
15 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
16 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
17 APPPATH\Controllers\Page.php(7): view('home', [...])
18 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
19 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
20 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
21 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
22 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
23 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
24 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
DEBUG - 2025-06-20 09:05:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-20 09:05:13 --> Error connecting to the database: mysqli_sql_exception: Unknown database 'sukses' in D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php:201
Stack trace:
#0 D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php(201): mysqli->real_connect('localhost', 'root', Object(SensitiveParameterValue), 'sukses', 3306, '', 0)
#1 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#2 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#3 D:\XAMPP\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 D:\XAMPP\htdocs\ci4\system\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#5 D:\XAMPP\htdocs\ci4\system\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
#6 D:\XAMPP\htdocs\ci4\app\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
#7 D:\XAMPP\htdocs\ci4\system\View\Cell.php(307): App\Cells\ArticleLatest->render(NULL)
#8 D:\XAMPP\htdocs\ci4\system\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', Array, 'App\\Cells\\Artic...')
#9 D:\XAMPP\htdocs\ci4\system\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\Artic...', Array, 0, 'AppCellsArticle...')
#10 D:\XAMPP\htdocs\ci4\app\Views\template\footer.php(4): view_cell('App\\Cells\\Artic...', Array)
#11 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#12 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#13 D:\XAMPP\htdocs\ci4\system\View\View.php(475): CodeIgniter\View\View->render('template/footer', NULL, true)
#14 D:\XAMPP\htdocs\ci4\app\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
#15 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#16 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#17 D:\XAMPP\htdocs\ci4\system\Common.php(1173): CodeIgniter\View\View->render('home', Array, true)
#18 D:\XAMPP\htdocs\ci4\app\Controllers\Page.php(7): view('home', Array)
#19 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#20 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#21 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#22 D:\XAMPP\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#23 D:\XAMPP\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#24 D:\XAMPP\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#25 D:\XAMPP\htdocs\ci4\system\rewrite.php(44): require_once('D:\\XAMPP\\htdocs...')
#26 {main}

Next CodeIgniter\Database\Exceptions\DatabaseException: Unknown database 'sukses' in D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php:246
Stack trace:
#0 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#1 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#2 D:\XAMPP\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#3 D:\XAMPP\htdocs\ci4\system\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#4 D:\XAMPP\htdocs\ci4\system\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
#5 D:\XAMPP\htdocs\ci4\app\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
#6 D:\XAMPP\htdocs\ci4\system\View\Cell.php(307): App\Cells\ArticleLatest->render(NULL)
#7 D:\XAMPP\htdocs\ci4\system\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', Array, 'App\\Cells\\Artic...')
#8 D:\XAMPP\htdocs\ci4\system\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\Artic...', Array, 0, 'AppCellsArticle...')
#9 D:\XAMPP\htdocs\ci4\app\Views\template\footer.php(4): view_cell('App\\Cells\\Artic...', Array)
#10 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#11 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#12 D:\XAMPP\htdocs\ci4\system\View\View.php(475): CodeIgniter\View\View->render('template/footer', NULL, true)
#13 D:\XAMPP\htdocs\ci4\app\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
#14 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#15 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#16 D:\XAMPP\htdocs\ci4\system\Common.php(1173): CodeIgniter\View\View->render('home', Array, true)
#17 D:\XAMPP\htdocs\ci4\app\Controllers\Page.php(7): view('home', Array)
#18 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#19 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#20 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#21 D:\XAMPP\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#22 D:\XAMPP\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#23 D:\XAMPP\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#24 D:\XAMPP\htdocs\ci4\system\rewrite.php(44): require_once('D:\\XAMPP\\htdocs...')
#25 {main}
CRITICAL - 2025-06-20 09:05:13 --> CodeIgniter\Database\Exceptions\DatabaseException: Unable to connect to the database.
Main connection [MySQLi]: Unknown database 'sukses'
[Method: GET, Route: /]
in SYSTEMPATH\Database\BaseConnection.php on line 467.
 1 SYSTEMPATH\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
 2 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 3 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 4 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 5 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 6 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 7 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 8 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
 9 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
10 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
11 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
12 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
13 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
14 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
15 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
16 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
17 APPPATH\Controllers\Page.php(7): view('home', [...])
18 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
19 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
20 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
21 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
22 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
23 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
24 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
DEBUG - 2025-06-20 09:05:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-20 09:05:14 --> Error connecting to the database: mysqli_sql_exception: Unknown database 'sukses' in D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php:201
Stack trace:
#0 D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php(201): mysqli->real_connect('localhost', 'root', Object(SensitiveParameterValue), 'sukses', 3306, '', 0)
#1 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#2 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#3 D:\XAMPP\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 D:\XAMPP\htdocs\ci4\system\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#5 D:\XAMPP\htdocs\ci4\system\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
#6 D:\XAMPP\htdocs\ci4\app\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
#7 D:\XAMPP\htdocs\ci4\system\View\Cell.php(307): App\Cells\ArticleLatest->render(NULL)
#8 D:\XAMPP\htdocs\ci4\system\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', Array, 'App\\Cells\\Artic...')
#9 D:\XAMPP\htdocs\ci4\system\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\Artic...', Array, 0, 'AppCellsArticle...')
#10 D:\XAMPP\htdocs\ci4\app\Views\template\footer.php(4): view_cell('App\\Cells\\Artic...', Array)
#11 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#12 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#13 D:\XAMPP\htdocs\ci4\system\View\View.php(475): CodeIgniter\View\View->render('template/footer', NULL, true)
#14 D:\XAMPP\htdocs\ci4\app\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
#15 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#16 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#17 D:\XAMPP\htdocs\ci4\system\Common.php(1173): CodeIgniter\View\View->render('home', Array, true)
#18 D:\XAMPP\htdocs\ci4\app\Controllers\Page.php(7): view('home', Array)
#19 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#20 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#21 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#22 D:\XAMPP\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#23 D:\XAMPP\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#24 D:\XAMPP\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#25 D:\XAMPP\htdocs\ci4\system\rewrite.php(44): require_once('D:\\XAMPP\\htdocs...')
#26 {main}

Next CodeIgniter\Database\Exceptions\DatabaseException: Unknown database 'sukses' in D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php:246
Stack trace:
#0 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#1 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#2 D:\XAMPP\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#3 D:\XAMPP\htdocs\ci4\system\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#4 D:\XAMPP\htdocs\ci4\system\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
#5 D:\XAMPP\htdocs\ci4\app\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
#6 D:\XAMPP\htdocs\ci4\system\View\Cell.php(307): App\Cells\ArticleLatest->render(NULL)
#7 D:\XAMPP\htdocs\ci4\system\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', Array, 'App\\Cells\\Artic...')
#8 D:\XAMPP\htdocs\ci4\system\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\Artic...', Array, 0, 'AppCellsArticle...')
#9 D:\XAMPP\htdocs\ci4\app\Views\template\footer.php(4): view_cell('App\\Cells\\Artic...', Array)
#10 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#11 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#12 D:\XAMPP\htdocs\ci4\system\View\View.php(475): CodeIgniter\View\View->render('template/footer', NULL, true)
#13 D:\XAMPP\htdocs\ci4\app\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
#14 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#15 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#16 D:\XAMPP\htdocs\ci4\system\Common.php(1173): CodeIgniter\View\View->render('home', Array, true)
#17 D:\XAMPP\htdocs\ci4\app\Controllers\Page.php(7): view('home', Array)
#18 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#19 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#20 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#21 D:\XAMPP\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#22 D:\XAMPP\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#23 D:\XAMPP\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#24 D:\XAMPP\htdocs\ci4\system\rewrite.php(44): require_once('D:\\XAMPP\\htdocs...')
#25 {main}
CRITICAL - 2025-06-20 09:05:14 --> CodeIgniter\Database\Exceptions\DatabaseException: Unable to connect to the database.
Main connection [MySQLi]: Unknown database 'sukses'
[Method: GET, Route: /]
in SYSTEMPATH\Database\BaseConnection.php on line 467.
 1 SYSTEMPATH\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
 2 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 3 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 4 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 5 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 6 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 7 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 8 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
 9 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
10 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
11 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
12 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
13 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
14 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
15 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
16 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
17 APPPATH\Controllers\Page.php(7): view('home', [...])
18 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
19 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
20 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
21 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
22 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
23 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
24 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
DEBUG - 2025-06-20 09:05:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-20 09:05:15 --> Error connecting to the database: mysqli_sql_exception: Unknown database 'sukses' in D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php:201
Stack trace:
#0 D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php(201): mysqli->real_connect('localhost', 'root', Object(SensitiveParameterValue), 'sukses', 3306, '', 0)
#1 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#2 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#3 D:\XAMPP\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 D:\XAMPP\htdocs\ci4\system\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#5 D:\XAMPP\htdocs\ci4\system\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
#6 D:\XAMPP\htdocs\ci4\app\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
#7 D:\XAMPP\htdocs\ci4\system\View\Cell.php(307): App\Cells\ArticleLatest->render(NULL)
#8 D:\XAMPP\htdocs\ci4\system\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', Array, 'App\\Cells\\Artic...')
#9 D:\XAMPP\htdocs\ci4\system\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\Artic...', Array, 0, 'AppCellsArticle...')
#10 D:\XAMPP\htdocs\ci4\app\Views\template\footer.php(4): view_cell('App\\Cells\\Artic...', Array)
#11 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#12 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#13 D:\XAMPP\htdocs\ci4\system\View\View.php(475): CodeIgniter\View\View->render('template/footer', NULL, true)
#14 D:\XAMPP\htdocs\ci4\app\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
#15 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#16 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#17 D:\XAMPP\htdocs\ci4\system\Common.php(1173): CodeIgniter\View\View->render('home', Array, true)
#18 D:\XAMPP\htdocs\ci4\app\Controllers\Page.php(7): view('home', Array)
#19 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#20 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#21 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#22 D:\XAMPP\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#23 D:\XAMPP\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#24 D:\XAMPP\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#25 D:\XAMPP\htdocs\ci4\system\rewrite.php(44): require_once('D:\\XAMPP\\htdocs...')
#26 {main}

Next CodeIgniter\Database\Exceptions\DatabaseException: Unknown database 'sukses' in D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php:246
Stack trace:
#0 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#1 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#2 D:\XAMPP\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#3 D:\XAMPP\htdocs\ci4\system\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#4 D:\XAMPP\htdocs\ci4\system\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
#5 D:\XAMPP\htdocs\ci4\app\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
#6 D:\XAMPP\htdocs\ci4\system\View\Cell.php(307): App\Cells\ArticleLatest->render(NULL)
#7 D:\XAMPP\htdocs\ci4\system\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', Array, 'App\\Cells\\Artic...')
#8 D:\XAMPP\htdocs\ci4\system\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\Artic...', Array, 0, 'AppCellsArticle...')
#9 D:\XAMPP\htdocs\ci4\app\Views\template\footer.php(4): view_cell('App\\Cells\\Artic...', Array)
#10 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#11 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#12 D:\XAMPP\htdocs\ci4\system\View\View.php(475): CodeIgniter\View\View->render('template/footer', NULL, true)
#13 D:\XAMPP\htdocs\ci4\app\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
#14 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#15 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#16 D:\XAMPP\htdocs\ci4\system\Common.php(1173): CodeIgniter\View\View->render('home', Array, true)
#17 D:\XAMPP\htdocs\ci4\app\Controllers\Page.php(7): view('home', Array)
#18 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#19 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#20 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#21 D:\XAMPP\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#22 D:\XAMPP\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#23 D:\XAMPP\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#24 D:\XAMPP\htdocs\ci4\system\rewrite.php(44): require_once('D:\\XAMPP\\htdocs...')
#25 {main}
CRITICAL - 2025-06-20 09:05:15 --> CodeIgniter\Database\Exceptions\DatabaseException: Unable to connect to the database.
Main connection [MySQLi]: Unknown database 'sukses'
[Method: GET, Route: /]
in SYSTEMPATH\Database\BaseConnection.php on line 467.
 1 SYSTEMPATH\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
 2 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 3 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 4 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 5 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 6 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 7 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 8 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
 9 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
10 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
11 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
12 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
13 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
14 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
15 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
16 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
17 APPPATH\Controllers\Page.php(7): view('home', [...])
18 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
19 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
20 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
21 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
22 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
23 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
24 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
DEBUG - 2025-06-20 09:05:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-20 09:05:21 --> Error connecting to the database: mysqli_sql_exception: Unknown database 'sukses' in D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php:201
Stack trace:
#0 D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php(201): mysqli->real_connect('localhost', 'root', Object(SensitiveParameterValue), 'sukses', 3306, '', 0)
#1 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#2 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#3 D:\XAMPP\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 D:\XAMPP\htdocs\ci4\system\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#5 D:\XAMPP\htdocs\ci4\system\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
#6 D:\XAMPP\htdocs\ci4\app\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
#7 D:\XAMPP\htdocs\ci4\system\View\Cell.php(307): App\Cells\ArticleLatest->render(NULL)
#8 D:\XAMPP\htdocs\ci4\system\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', Array, 'App\\Cells\\Artic...')
#9 D:\XAMPP\htdocs\ci4\system\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\Artic...', Array, 0, 'AppCellsArticle...')
#10 D:\XAMPP\htdocs\ci4\app\Views\template\footer.php(4): view_cell('App\\Cells\\Artic...', Array)
#11 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#12 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#13 D:\XAMPP\htdocs\ci4\system\View\View.php(475): CodeIgniter\View\View->render('template/footer', NULL, true)
#14 D:\XAMPP\htdocs\ci4\app\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
#15 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#16 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#17 D:\XAMPP\htdocs\ci4\system\Common.php(1173): CodeIgniter\View\View->render('home', Array, true)
#18 D:\XAMPP\htdocs\ci4\app\Controllers\Page.php(7): view('home', Array)
#19 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#20 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#21 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#22 D:\XAMPP\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#23 D:\XAMPP\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#24 D:\XAMPP\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#25 D:\XAMPP\htdocs\ci4\system\rewrite.php(44): require_once('D:\\XAMPP\\htdocs...')
#26 {main}

Next CodeIgniter\Database\Exceptions\DatabaseException: Unknown database 'sukses' in D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php:246
Stack trace:
#0 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#1 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#2 D:\XAMPP\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#3 D:\XAMPP\htdocs\ci4\system\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#4 D:\XAMPP\htdocs\ci4\system\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
#5 D:\XAMPP\htdocs\ci4\app\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
#6 D:\XAMPP\htdocs\ci4\system\View\Cell.php(307): App\Cells\ArticleLatest->render(NULL)
#7 D:\XAMPP\htdocs\ci4\system\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', Array, 'App\\Cells\\Artic...')
#8 D:\XAMPP\htdocs\ci4\system\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\Artic...', Array, 0, 'AppCellsArticle...')
#9 D:\XAMPP\htdocs\ci4\app\Views\template\footer.php(4): view_cell('App\\Cells\\Artic...', Array)
#10 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#11 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#12 D:\XAMPP\htdocs\ci4\system\View\View.php(475): CodeIgniter\View\View->render('template/footer', NULL, true)
#13 D:\XAMPP\htdocs\ci4\app\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
#14 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#15 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#16 D:\XAMPP\htdocs\ci4\system\Common.php(1173): CodeIgniter\View\View->render('home', Array, true)
#17 D:\XAMPP\htdocs\ci4\app\Controllers\Page.php(7): view('home', Array)
#18 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#19 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#20 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#21 D:\XAMPP\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#22 D:\XAMPP\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#23 D:\XAMPP\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#24 D:\XAMPP\htdocs\ci4\system\rewrite.php(44): require_once('D:\\XAMPP\\htdocs...')
#25 {main}
CRITICAL - 2025-06-20 09:05:21 --> CodeIgniter\Database\Exceptions\DatabaseException: Unable to connect to the database.
Main connection [MySQLi]: Unknown database 'sukses'
[Method: GET, Route: /]
in SYSTEMPATH\Database\BaseConnection.php on line 467.
 1 SYSTEMPATH\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
 2 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 3 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 4 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 5 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 6 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 7 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 8 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
 9 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
10 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
11 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
12 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
13 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
14 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
15 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
16 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
17 APPPATH\Controllers\Page.php(7): view('home', [...])
18 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
19 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
20 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
21 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
22 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
23 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
24 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
DEBUG - 2025-06-20 09:05:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-20 09:05:22 --> Error connecting to the database: mysqli_sql_exception: Unknown database 'sukses' in D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php:201
Stack trace:
#0 D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php(201): mysqli->real_connect('localhost', 'root', Object(SensitiveParameterValue), 'sukses', 3306, '', 0)
#1 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#2 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#3 D:\XAMPP\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 D:\XAMPP\htdocs\ci4\system\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#5 D:\XAMPP\htdocs\ci4\system\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
#6 D:\XAMPP\htdocs\ci4\app\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
#7 D:\XAMPP\htdocs\ci4\system\View\Cell.php(307): App\Cells\ArticleLatest->render(NULL)
#8 D:\XAMPP\htdocs\ci4\system\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', Array, 'App\\Cells\\Artic...')
#9 D:\XAMPP\htdocs\ci4\system\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\Artic...', Array, 0, 'AppCellsArticle...')
#10 D:\XAMPP\htdocs\ci4\app\Views\template\footer.php(4): view_cell('App\\Cells\\Artic...', Array)
#11 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#12 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#13 D:\XAMPP\htdocs\ci4\system\View\View.php(475): CodeIgniter\View\View->render('template/footer', NULL, true)
#14 D:\XAMPP\htdocs\ci4\app\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
#15 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#16 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#17 D:\XAMPP\htdocs\ci4\system\Common.php(1173): CodeIgniter\View\View->render('home', Array, true)
#18 D:\XAMPP\htdocs\ci4\app\Controllers\Page.php(7): view('home', Array)
#19 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#20 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#21 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#22 D:\XAMPP\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#23 D:\XAMPP\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#24 D:\XAMPP\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#25 D:\XAMPP\htdocs\ci4\system\rewrite.php(44): require_once('D:\\XAMPP\\htdocs...')
#26 {main}

Next CodeIgniter\Database\Exceptions\DatabaseException: Unknown database 'sukses' in D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php:246
Stack trace:
#0 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#1 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#2 D:\XAMPP\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#3 D:\XAMPP\htdocs\ci4\system\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#4 D:\XAMPP\htdocs\ci4\system\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
#5 D:\XAMPP\htdocs\ci4\app\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
#6 D:\XAMPP\htdocs\ci4\system\View\Cell.php(307): App\Cells\ArticleLatest->render(NULL)
#7 D:\XAMPP\htdocs\ci4\system\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', Array, 'App\\Cells\\Artic...')
#8 D:\XAMPP\htdocs\ci4\system\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\Artic...', Array, 0, 'AppCellsArticle...')
#9 D:\XAMPP\htdocs\ci4\app\Views\template\footer.php(4): view_cell('App\\Cells\\Artic...', Array)
#10 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#11 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#12 D:\XAMPP\htdocs\ci4\system\View\View.php(475): CodeIgniter\View\View->render('template/footer', NULL, true)
#13 D:\XAMPP\htdocs\ci4\app\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
#14 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#15 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#16 D:\XAMPP\htdocs\ci4\system\Common.php(1173): CodeIgniter\View\View->render('home', Array, true)
#17 D:\XAMPP\htdocs\ci4\app\Controllers\Page.php(7): view('home', Array)
#18 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#19 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#20 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#21 D:\XAMPP\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#22 D:\XAMPP\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#23 D:\XAMPP\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#24 D:\XAMPP\htdocs\ci4\system\rewrite.php(44): require_once('D:\\XAMPP\\htdocs...')
#25 {main}
CRITICAL - 2025-06-20 09:05:22 --> CodeIgniter\Database\Exceptions\DatabaseException: Unable to connect to the database.
Main connection [MySQLi]: Unknown database 'sukses'
[Method: GET, Route: /]
in SYSTEMPATH\Database\BaseConnection.php on line 467.
 1 SYSTEMPATH\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
 2 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 3 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 4 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 5 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 6 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 7 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 8 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
 9 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
10 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
11 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
12 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
13 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
14 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
15 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
16 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
17 APPPATH\Controllers\Page.php(7): view('home', [...])
18 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
19 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
20 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
21 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
22 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
23 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
24 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
DEBUG - 2025-06-20 09:05:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-20 09:05:25 --> Error connecting to the database: mysqli_sql_exception: Unknown database 'sukses' in D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php:201
Stack trace:
#0 D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php(201): mysqli->real_connect('localhost', 'root', Object(SensitiveParameterValue), 'sukses', 3306, '', 0)
#1 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#2 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#3 D:\XAMPP\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 D:\XAMPP\htdocs\ci4\system\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#5 D:\XAMPP\htdocs\ci4\system\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
#6 D:\XAMPP\htdocs\ci4\app\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
#7 D:\XAMPP\htdocs\ci4\system\View\Cell.php(307): App\Cells\ArticleLatest->render(NULL)
#8 D:\XAMPP\htdocs\ci4\system\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', Array, 'App\\Cells\\Artic...')
#9 D:\XAMPP\htdocs\ci4\system\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\Artic...', Array, 0, 'AppCellsArticle...')
#10 D:\XAMPP\htdocs\ci4\app\Views\template\footer.php(4): view_cell('App\\Cells\\Artic...', Array)
#11 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#12 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#13 D:\XAMPP\htdocs\ci4\system\View\View.php(475): CodeIgniter\View\View->render('template/footer', NULL, true)
#14 D:\XAMPP\htdocs\ci4\app\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
#15 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#16 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#17 D:\XAMPP\htdocs\ci4\system\Common.php(1173): CodeIgniter\View\View->render('home', Array, true)
#18 D:\XAMPP\htdocs\ci4\app\Controllers\Page.php(7): view('home', Array)
#19 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#20 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#21 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#22 D:\XAMPP\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#23 D:\XAMPP\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#24 D:\XAMPP\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#25 D:\XAMPP\htdocs\ci4\system\rewrite.php(44): require_once('D:\\XAMPP\\htdocs...')
#26 {main}

Next CodeIgniter\Database\Exceptions\DatabaseException: Unknown database 'sukses' in D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php:246
Stack trace:
#0 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#1 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#2 D:\XAMPP\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#3 D:\XAMPP\htdocs\ci4\system\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#4 D:\XAMPP\htdocs\ci4\system\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
#5 D:\XAMPP\htdocs\ci4\app\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
#6 D:\XAMPP\htdocs\ci4\system\View\Cell.php(307): App\Cells\ArticleLatest->render(NULL)
#7 D:\XAMPP\htdocs\ci4\system\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', Array, 'App\\Cells\\Artic...')
#8 D:\XAMPP\htdocs\ci4\system\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\Artic...', Array, 0, 'AppCellsArticle...')
#9 D:\XAMPP\htdocs\ci4\app\Views\template\footer.php(4): view_cell('App\\Cells\\Artic...', Array)
#10 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#11 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#12 D:\XAMPP\htdocs\ci4\system\View\View.php(475): CodeIgniter\View\View->render('template/footer', NULL, true)
#13 D:\XAMPP\htdocs\ci4\app\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
#14 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#15 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#16 D:\XAMPP\htdocs\ci4\system\Common.php(1173): CodeIgniter\View\View->render('home', Array, true)
#17 D:\XAMPP\htdocs\ci4\app\Controllers\Page.php(7): view('home', Array)
#18 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#19 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#20 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#21 D:\XAMPP\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#22 D:\XAMPP\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#23 D:\XAMPP\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#24 D:\XAMPP\htdocs\ci4\system\rewrite.php(44): require_once('D:\\XAMPP\\htdocs...')
#25 {main}
CRITICAL - 2025-06-20 09:05:25 --> CodeIgniter\Database\Exceptions\DatabaseException: Unable to connect to the database.
Main connection [MySQLi]: Unknown database 'sukses'
[Method: GET, Route: /]
in SYSTEMPATH\Database\BaseConnection.php on line 467.
 1 SYSTEMPATH\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
 2 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 3 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 4 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 5 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 6 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 7 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 8 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
 9 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
10 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
11 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
12 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
13 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
14 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
15 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
16 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
17 APPPATH\Controllers\Page.php(7): view('home', [...])
18 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
19 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
20 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
21 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
22 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
23 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
24 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
DEBUG - 2025-06-20 09:05:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-20 09:05:25 --> Error connecting to the database: mysqli_sql_exception: Unknown database 'sukses' in D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php:201
Stack trace:
#0 D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php(201): mysqli->real_connect('localhost', 'root', Object(SensitiveParameterValue), 'sukses', 3306, '', 0)
#1 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#2 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#3 D:\XAMPP\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 D:\XAMPP\htdocs\ci4\system\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#5 D:\XAMPP\htdocs\ci4\system\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
#6 D:\XAMPP\htdocs\ci4\app\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
#7 D:\XAMPP\htdocs\ci4\system\View\Cell.php(307): App\Cells\ArticleLatest->render(NULL)
#8 D:\XAMPP\htdocs\ci4\system\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', Array, 'App\\Cells\\Artic...')
#9 D:\XAMPP\htdocs\ci4\system\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\Artic...', Array, 0, 'AppCellsArticle...')
#10 D:\XAMPP\htdocs\ci4\app\Views\template\footer.php(4): view_cell('App\\Cells\\Artic...', Array)
#11 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#12 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#13 D:\XAMPP\htdocs\ci4\system\View\View.php(475): CodeIgniter\View\View->render('template/footer', NULL, true)
#14 D:\XAMPP\htdocs\ci4\app\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
#15 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#16 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#17 D:\XAMPP\htdocs\ci4\system\Common.php(1173): CodeIgniter\View\View->render('home', Array, true)
#18 D:\XAMPP\htdocs\ci4\app\Controllers\Page.php(7): view('home', Array)
#19 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#20 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#21 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#22 D:\XAMPP\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#23 D:\XAMPP\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#24 D:\XAMPP\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#25 D:\XAMPP\htdocs\ci4\system\rewrite.php(44): require_once('D:\\XAMPP\\htdocs...')
#26 {main}

Next CodeIgniter\Database\Exceptions\DatabaseException: Unknown database 'sukses' in D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php:246
Stack trace:
#0 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#1 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#2 D:\XAMPP\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#3 D:\XAMPP\htdocs\ci4\system\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#4 D:\XAMPP\htdocs\ci4\system\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
#5 D:\XAMPP\htdocs\ci4\app\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
#6 D:\XAMPP\htdocs\ci4\system\View\Cell.php(307): App\Cells\ArticleLatest->render(NULL)
#7 D:\XAMPP\htdocs\ci4\system\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', Array, 'App\\Cells\\Artic...')
#8 D:\XAMPP\htdocs\ci4\system\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\Artic...', Array, 0, 'AppCellsArticle...')
#9 D:\XAMPP\htdocs\ci4\app\Views\template\footer.php(4): view_cell('App\\Cells\\Artic...', Array)
#10 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#11 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#12 D:\XAMPP\htdocs\ci4\system\View\View.php(475): CodeIgniter\View\View->render('template/footer', NULL, true)
#13 D:\XAMPP\htdocs\ci4\app\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
#14 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#15 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#16 D:\XAMPP\htdocs\ci4\system\Common.php(1173): CodeIgniter\View\View->render('home', Array, true)
#17 D:\XAMPP\htdocs\ci4\app\Controllers\Page.php(7): view('home', Array)
#18 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#19 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#20 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#21 D:\XAMPP\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#22 D:\XAMPP\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#23 D:\XAMPP\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#24 D:\XAMPP\htdocs\ci4\system\rewrite.php(44): require_once('D:\\XAMPP\\htdocs...')
#25 {main}
CRITICAL - 2025-06-20 09:05:25 --> CodeIgniter\Database\Exceptions\DatabaseException: Unable to connect to the database.
Main connection [MySQLi]: Unknown database 'sukses'
[Method: GET, Route: /]
in SYSTEMPATH\Database\BaseConnection.php on line 467.
 1 SYSTEMPATH\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
 2 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 3 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 4 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 5 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 6 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 7 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 8 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
 9 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
10 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
11 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
12 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
13 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
14 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
15 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
16 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
17 APPPATH\Controllers\Page.php(7): view('home', [...])
18 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
19 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
20 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
21 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
22 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
23 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
24 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
DEBUG - 2025-06-20 09:05:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-20 09:05:26 --> Error connecting to the database: mysqli_sql_exception: Unknown database 'sukses' in D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php:201
Stack trace:
#0 D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php(201): mysqli->real_connect('localhost', 'root', Object(SensitiveParameterValue), 'sukses', 3306, '', 0)
#1 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#2 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#3 D:\XAMPP\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 D:\XAMPP\htdocs\ci4\system\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#5 D:\XAMPP\htdocs\ci4\system\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
#6 D:\XAMPP\htdocs\ci4\app\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
#7 D:\XAMPP\htdocs\ci4\system\View\Cell.php(307): App\Cells\ArticleLatest->render(NULL)
#8 D:\XAMPP\htdocs\ci4\system\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', Array, 'App\\Cells\\Artic...')
#9 D:\XAMPP\htdocs\ci4\system\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\Artic...', Array, 0, 'AppCellsArticle...')
#10 D:\XAMPP\htdocs\ci4\app\Views\template\footer.php(4): view_cell('App\\Cells\\Artic...', Array)
#11 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#12 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#13 D:\XAMPP\htdocs\ci4\system\View\View.php(475): CodeIgniter\View\View->render('template/footer', NULL, true)
#14 D:\XAMPP\htdocs\ci4\app\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
#15 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#16 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#17 D:\XAMPP\htdocs\ci4\system\Common.php(1173): CodeIgniter\View\View->render('home', Array, true)
#18 D:\XAMPP\htdocs\ci4\app\Controllers\Page.php(7): view('home', Array)
#19 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#20 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#21 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#22 D:\XAMPP\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#23 D:\XAMPP\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#24 D:\XAMPP\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#25 D:\XAMPP\htdocs\ci4\system\rewrite.php(44): require_once('D:\\XAMPP\\htdocs...')
#26 {main}

Next CodeIgniter\Database\Exceptions\DatabaseException: Unknown database 'sukses' in D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php:246
Stack trace:
#0 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#1 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#2 D:\XAMPP\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#3 D:\XAMPP\htdocs\ci4\system\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#4 D:\XAMPP\htdocs\ci4\system\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
#5 D:\XAMPP\htdocs\ci4\app\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
#6 D:\XAMPP\htdocs\ci4\system\View\Cell.php(307): App\Cells\ArticleLatest->render(NULL)
#7 D:\XAMPP\htdocs\ci4\system\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', Array, 'App\\Cells\\Artic...')
#8 D:\XAMPP\htdocs\ci4\system\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\Artic...', Array, 0, 'AppCellsArticle...')
#9 D:\XAMPP\htdocs\ci4\app\Views\template\footer.php(4): view_cell('App\\Cells\\Artic...', Array)
#10 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#11 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#12 D:\XAMPP\htdocs\ci4\system\View\View.php(475): CodeIgniter\View\View->render('template/footer', NULL, true)
#13 D:\XAMPP\htdocs\ci4\app\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
#14 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#15 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#16 D:\XAMPP\htdocs\ci4\system\Common.php(1173): CodeIgniter\View\View->render('home', Array, true)
#17 D:\XAMPP\htdocs\ci4\app\Controllers\Page.php(7): view('home', Array)
#18 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#19 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#20 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#21 D:\XAMPP\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#22 D:\XAMPP\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#23 D:\XAMPP\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#24 D:\XAMPP\htdocs\ci4\system\rewrite.php(44): require_once('D:\\XAMPP\\htdocs...')
#25 {main}
CRITICAL - 2025-06-20 09:05:26 --> CodeIgniter\Database\Exceptions\DatabaseException: Unable to connect to the database.
Main connection [MySQLi]: Unknown database 'sukses'
[Method: GET, Route: /]
in SYSTEMPATH\Database\BaseConnection.php on line 467.
 1 SYSTEMPATH\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
 2 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 3 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 4 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 5 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 6 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 7 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 8 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
 9 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
10 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
11 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
12 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
13 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
14 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
15 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
16 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
17 APPPATH\Controllers\Page.php(7): view('home', [...])
18 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
19 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
20 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
21 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
22 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
23 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
24 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
DEBUG - 2025-06-20 09:05:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-20 09:05:26 --> Error connecting to the database: mysqli_sql_exception: Unknown database 'sukses' in D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php:201
Stack trace:
#0 D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php(201): mysqli->real_connect('localhost', 'root', Object(SensitiveParameterValue), 'sukses', 3306, '', 0)
#1 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#2 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#3 D:\XAMPP\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 D:\XAMPP\htdocs\ci4\system\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#5 D:\XAMPP\htdocs\ci4\system\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
#6 D:\XAMPP\htdocs\ci4\app\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
#7 D:\XAMPP\htdocs\ci4\system\View\Cell.php(307): App\Cells\ArticleLatest->render(NULL)
#8 D:\XAMPP\htdocs\ci4\system\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', Array, 'App\\Cells\\Artic...')
#9 D:\XAMPP\htdocs\ci4\system\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\Artic...', Array, 0, 'AppCellsArticle...')
#10 D:\XAMPP\htdocs\ci4\app\Views\template\footer.php(4): view_cell('App\\Cells\\Artic...', Array)
#11 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#12 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#13 D:\XAMPP\htdocs\ci4\system\View\View.php(475): CodeIgniter\View\View->render('template/footer', NULL, true)
#14 D:\XAMPP\htdocs\ci4\app\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
#15 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#16 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#17 D:\XAMPP\htdocs\ci4\system\Common.php(1173): CodeIgniter\View\View->render('home', Array, true)
#18 D:\XAMPP\htdocs\ci4\app\Controllers\Page.php(7): view('home', Array)
#19 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#20 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#21 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#22 D:\XAMPP\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#23 D:\XAMPP\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#24 D:\XAMPP\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#25 D:\XAMPP\htdocs\ci4\system\rewrite.php(44): require_once('D:\\XAMPP\\htdocs...')
#26 {main}

Next CodeIgniter\Database\Exceptions\DatabaseException: Unknown database 'sukses' in D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php:246
Stack trace:
#0 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#1 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#2 D:\XAMPP\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#3 D:\XAMPP\htdocs\ci4\system\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#4 D:\XAMPP\htdocs\ci4\system\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
#5 D:\XAMPP\htdocs\ci4\app\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
#6 D:\XAMPP\htdocs\ci4\system\View\Cell.php(307): App\Cells\ArticleLatest->render(NULL)
#7 D:\XAMPP\htdocs\ci4\system\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', Array, 'App\\Cells\\Artic...')
#8 D:\XAMPP\htdocs\ci4\system\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\Artic...', Array, 0, 'AppCellsArticle...')
#9 D:\XAMPP\htdocs\ci4\app\Views\template\footer.php(4): view_cell('App\\Cells\\Artic...', Array)
#10 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#11 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#12 D:\XAMPP\htdocs\ci4\system\View\View.php(475): CodeIgniter\View\View->render('template/footer', NULL, true)
#13 D:\XAMPP\htdocs\ci4\app\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
#14 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#15 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#16 D:\XAMPP\htdocs\ci4\system\Common.php(1173): CodeIgniter\View\View->render('home', Array, true)
#17 D:\XAMPP\htdocs\ci4\app\Controllers\Page.php(7): view('home', Array)
#18 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#19 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#20 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#21 D:\XAMPP\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#22 D:\XAMPP\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#23 D:\XAMPP\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#24 D:\XAMPP\htdocs\ci4\system\rewrite.php(44): require_once('D:\\XAMPP\\htdocs...')
#25 {main}
CRITICAL - 2025-06-20 09:05:26 --> CodeIgniter\Database\Exceptions\DatabaseException: Unable to connect to the database.
Main connection [MySQLi]: Unknown database 'sukses'
[Method: GET, Route: /]
in SYSTEMPATH\Database\BaseConnection.php on line 467.
 1 SYSTEMPATH\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
 2 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 3 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 4 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 5 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 6 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 7 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 8 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
 9 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
10 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
11 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
12 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
13 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
14 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
15 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
16 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
17 APPPATH\Controllers\Page.php(7): view('home', [...])
18 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
19 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
20 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
21 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
22 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
23 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
24 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
DEBUG - 2025-06-20 09:05:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-20 09:05:57 --> Error connecting to the database: mysqli_sql_exception: Unknown database 'sukses' in D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php:201
Stack trace:
#0 D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php(201): mysqli->real_connect('localhost', 'root', Object(SensitiveParameterValue), 'sukses', 3306, '', 0)
#1 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#2 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#3 D:\XAMPP\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 D:\XAMPP\htdocs\ci4\system\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#5 D:\XAMPP\htdocs\ci4\system\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
#6 D:\XAMPP\htdocs\ci4\app\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
#7 D:\XAMPP\htdocs\ci4\system\View\Cell.php(307): App\Cells\ArticleLatest->render(NULL)
#8 D:\XAMPP\htdocs\ci4\system\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', Array, 'App\\Cells\\Artic...')
#9 D:\XAMPP\htdocs\ci4\system\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\Artic...', Array, 0, 'AppCellsArticle...')
#10 D:\XAMPP\htdocs\ci4\app\Views\template\footer.php(4): view_cell('App\\Cells\\Artic...', Array)
#11 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#12 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#13 D:\XAMPP\htdocs\ci4\system\View\View.php(475): CodeIgniter\View\View->render('template/footer', NULL, true)
#14 D:\XAMPP\htdocs\ci4\app\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
#15 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#16 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#17 D:\XAMPP\htdocs\ci4\system\Common.php(1173): CodeIgniter\View\View->render('home', Array, true)
#18 D:\XAMPP\htdocs\ci4\app\Controllers\Page.php(7): view('home', Array)
#19 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#20 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#21 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#22 D:\XAMPP\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#23 D:\XAMPP\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#24 D:\XAMPP\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#25 D:\XAMPP\htdocs\ci4\system\rewrite.php(44): require_once('D:\\XAMPP\\htdocs...')
#26 {main}

Next CodeIgniter\Database\Exceptions\DatabaseException: Unknown database 'sukses' in D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php:246
Stack trace:
#0 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#1 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#2 D:\XAMPP\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#3 D:\XAMPP\htdocs\ci4\system\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#4 D:\XAMPP\htdocs\ci4\system\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
#5 D:\XAMPP\htdocs\ci4\app\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
#6 D:\XAMPP\htdocs\ci4\system\View\Cell.php(307): App\Cells\ArticleLatest->render(NULL)
#7 D:\XAMPP\htdocs\ci4\system\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', Array, 'App\\Cells\\Artic...')
#8 D:\XAMPP\htdocs\ci4\system\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\Artic...', Array, 0, 'AppCellsArticle...')
#9 D:\XAMPP\htdocs\ci4\app\Views\template\footer.php(4): view_cell('App\\Cells\\Artic...', Array)
#10 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#11 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#12 D:\XAMPP\htdocs\ci4\system\View\View.php(475): CodeIgniter\View\View->render('template/footer', NULL, true)
#13 D:\XAMPP\htdocs\ci4\app\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
#14 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#15 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#16 D:\XAMPP\htdocs\ci4\system\Common.php(1173): CodeIgniter\View\View->render('home', Array, true)
#17 D:\XAMPP\htdocs\ci4\app\Controllers\Page.php(7): view('home', Array)
#18 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#19 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#20 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#21 D:\XAMPP\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#22 D:\XAMPP\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#23 D:\XAMPP\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#24 D:\XAMPP\htdocs\ci4\system\rewrite.php(44): require_once('D:\\XAMPP\\htdocs...')
#25 {main}
CRITICAL - 2025-06-20 09:05:57 --> CodeIgniter\Database\Exceptions\DatabaseException: Unable to connect to the database.
Main connection [MySQLi]: Unknown database 'sukses'
[Method: GET, Route: /]
in SYSTEMPATH\Database\BaseConnection.php on line 467.
 1 SYSTEMPATH\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
 2 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 3 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 4 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 5 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 6 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 7 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 8 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
 9 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
10 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
11 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
12 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
13 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
14 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
15 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
16 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
17 APPPATH\Controllers\Page.php(7): view('home', [...])
18 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
19 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
20 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
21 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
22 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
23 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
24 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
DEBUG - 2025-06-20 09:05:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-20 09:05:57 --> Error connecting to the database: mysqli_sql_exception: Unknown database 'sukses' in D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php:201
Stack trace:
#0 D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php(201): mysqli->real_connect('localhost', 'root', Object(SensitiveParameterValue), 'sukses', 3306, '', 0)
#1 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#2 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#3 D:\XAMPP\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 D:\XAMPP\htdocs\ci4\system\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#5 D:\XAMPP\htdocs\ci4\system\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
#6 D:\XAMPP\htdocs\ci4\app\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
#7 D:\XAMPP\htdocs\ci4\system\View\Cell.php(307): App\Cells\ArticleLatest->render(NULL)
#8 D:\XAMPP\htdocs\ci4\system\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', Array, 'App\\Cells\\Artic...')
#9 D:\XAMPP\htdocs\ci4\system\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\Artic...', Array, 0, 'AppCellsArticle...')
#10 D:\XAMPP\htdocs\ci4\app\Views\template\footer.php(4): view_cell('App\\Cells\\Artic...', Array)
#11 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#12 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#13 D:\XAMPP\htdocs\ci4\system\View\View.php(475): CodeIgniter\View\View->render('template/footer', NULL, true)
#14 D:\XAMPP\htdocs\ci4\app\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
#15 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#16 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#17 D:\XAMPP\htdocs\ci4\system\Common.php(1173): CodeIgniter\View\View->render('home', Array, true)
#18 D:\XAMPP\htdocs\ci4\app\Controllers\Page.php(7): view('home', Array)
#19 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#20 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#21 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#22 D:\XAMPP\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#23 D:\XAMPP\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#24 D:\XAMPP\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#25 D:\XAMPP\htdocs\ci4\system\rewrite.php(44): require_once('D:\\XAMPP\\htdocs...')
#26 {main}

Next CodeIgniter\Database\Exceptions\DatabaseException: Unknown database 'sukses' in D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php:246
Stack trace:
#0 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#1 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#2 D:\XAMPP\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#3 D:\XAMPP\htdocs\ci4\system\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#4 D:\XAMPP\htdocs\ci4\system\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
#5 D:\XAMPP\htdocs\ci4\app\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
#6 D:\XAMPP\htdocs\ci4\system\View\Cell.php(307): App\Cells\ArticleLatest->render(NULL)
#7 D:\XAMPP\htdocs\ci4\system\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', Array, 'App\\Cells\\Artic...')
#8 D:\XAMPP\htdocs\ci4\system\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\Artic...', Array, 0, 'AppCellsArticle...')
#9 D:\XAMPP\htdocs\ci4\app\Views\template\footer.php(4): view_cell('App\\Cells\\Artic...', Array)
#10 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#11 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#12 D:\XAMPP\htdocs\ci4\system\View\View.php(475): CodeIgniter\View\View->render('template/footer', NULL, true)
#13 D:\XAMPP\htdocs\ci4\app\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
#14 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#15 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#16 D:\XAMPP\htdocs\ci4\system\Common.php(1173): CodeIgniter\View\View->render('home', Array, true)
#17 D:\XAMPP\htdocs\ci4\app\Controllers\Page.php(7): view('home', Array)
#18 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#19 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#20 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#21 D:\XAMPP\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#22 D:\XAMPP\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#23 D:\XAMPP\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#24 D:\XAMPP\htdocs\ci4\system\rewrite.php(44): require_once('D:\\XAMPP\\htdocs...')
#25 {main}
CRITICAL - 2025-06-20 09:05:57 --> CodeIgniter\Database\Exceptions\DatabaseException: Unable to connect to the database.
Main connection [MySQLi]: Unknown database 'sukses'
[Method: GET, Route: /]
in SYSTEMPATH\Database\BaseConnection.php on line 467.
 1 SYSTEMPATH\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
 2 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 3 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 4 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 5 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 6 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 7 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 8 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
 9 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
10 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
11 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
12 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
13 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
14 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
15 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
16 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
17 APPPATH\Controllers\Page.php(7): view('home', [...])
18 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
19 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
20 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
21 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
22 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
23 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
24 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
DEBUG - 2025-06-20 09:22:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-20 09:22:55 --> Error connecting to the database: mysqli_sql_exception: Unknown database 'sukses' in D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php:201
Stack trace:
#0 D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php(201): mysqli->real_connect('localhost', 'root', Object(SensitiveParameterValue), 'sukses', 3306, '', 0)
#1 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#2 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#3 D:\XAMPP\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 D:\XAMPP\htdocs\ci4\system\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#5 D:\XAMPP\htdocs\ci4\system\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
#6 D:\XAMPP\htdocs\ci4\app\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
#7 D:\XAMPP\htdocs\ci4\system\View\Cell.php(307): App\Cells\ArticleLatest->render(NULL)
#8 D:\XAMPP\htdocs\ci4\system\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', Array, 'App\\Cells\\Artic...')
#9 D:\XAMPP\htdocs\ci4\system\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\Artic...', Array, 0, 'AppCellsArticle...')
#10 D:\XAMPP\htdocs\ci4\app\Views\template\footer.php(4): view_cell('App\\Cells\\Artic...', Array)
#11 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#12 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#13 D:\XAMPP\htdocs\ci4\system\View\View.php(475): CodeIgniter\View\View->render('template/footer', NULL, true)
#14 D:\XAMPP\htdocs\ci4\app\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
#15 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#16 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#17 D:\XAMPP\htdocs\ci4\system\Common.php(1173): CodeIgniter\View\View->render('home', Array, true)
#18 D:\XAMPP\htdocs\ci4\app\Controllers\Page.php(7): view('home', Array)
#19 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#20 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#21 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#22 D:\XAMPP\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#23 D:\XAMPP\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#24 D:\XAMPP\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#25 D:\XAMPP\htdocs\ci4\system\rewrite.php(44): require_once('D:\\XAMPP\\htdocs...')
#26 {main}

Next CodeIgniter\Database\Exceptions\DatabaseException: Unknown database 'sukses' in D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php:246
Stack trace:
#0 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#1 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#2 D:\XAMPP\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#3 D:\XAMPP\htdocs\ci4\system\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#4 D:\XAMPP\htdocs\ci4\system\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
#5 D:\XAMPP\htdocs\ci4\app\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
#6 D:\XAMPP\htdocs\ci4\system\View\Cell.php(307): App\Cells\ArticleLatest->render(NULL)
#7 D:\XAMPP\htdocs\ci4\system\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', Array, 'App\\Cells\\Artic...')
#8 D:\XAMPP\htdocs\ci4\system\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\Artic...', Array, 0, 'AppCellsArticle...')
#9 D:\XAMPP\htdocs\ci4\app\Views\template\footer.php(4): view_cell('App\\Cells\\Artic...', Array)
#10 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#11 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#12 D:\XAMPP\htdocs\ci4\system\View\View.php(475): CodeIgniter\View\View->render('template/footer', NULL, true)
#13 D:\XAMPP\htdocs\ci4\app\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
#14 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#15 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#16 D:\XAMPP\htdocs\ci4\system\Common.php(1173): CodeIgniter\View\View->render('home', Array, true)
#17 D:\XAMPP\htdocs\ci4\app\Controllers\Page.php(7): view('home', Array)
#18 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#19 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#20 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#21 D:\XAMPP\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#22 D:\XAMPP\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#23 D:\XAMPP\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#24 D:\XAMPP\htdocs\ci4\system\rewrite.php(44): require_once('D:\\XAMPP\\htdocs...')
#25 {main}
CRITICAL - 2025-06-20 09:22:55 --> CodeIgniter\Database\Exceptions\DatabaseException: Unable to connect to the database.
Main connection [MySQLi]: Unknown database 'sukses'
[Method: GET, Route: /]
in SYSTEMPATH\Database\BaseConnection.php on line 467.
 1 SYSTEMPATH\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
 2 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 3 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 4 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 5 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 6 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 7 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 8 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
 9 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
10 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
11 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
12 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
13 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
14 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
15 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
16 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
17 APPPATH\Controllers\Page.php(7): view('home', [...])
18 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
19 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
20 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
21 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
22 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
23 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
24 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
DEBUG - 2025-06-20 09:23:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-20 09:23:01 --> Error connecting to the database: mysqli_sql_exception: Unknown database 'sukses' in D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php:201
Stack trace:
#0 D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php(201): mysqli->real_connect('localhost', 'root', Object(SensitiveParameterValue), 'sukses', 3306, '', 0)
#1 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#2 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#3 D:\XAMPP\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 D:\XAMPP\htdocs\ci4\system\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#5 D:\XAMPP\htdocs\ci4\system\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
#6 D:\XAMPP\htdocs\ci4\app\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
#7 D:\XAMPP\htdocs\ci4\system\View\Cell.php(307): App\Cells\ArticleLatest->render(NULL)
#8 D:\XAMPP\htdocs\ci4\system\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', Array, 'App\\Cells\\Artic...')
#9 D:\XAMPP\htdocs\ci4\system\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\Artic...', Array, 0, 'AppCellsArticle...')
#10 D:\XAMPP\htdocs\ci4\app\Views\template\footer.php(4): view_cell('App\\Cells\\Artic...', Array)
#11 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#12 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#13 D:\XAMPP\htdocs\ci4\system\View\View.php(475): CodeIgniter\View\View->render('template/footer', NULL, true)
#14 D:\XAMPP\htdocs\ci4\app\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
#15 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#16 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#17 D:\XAMPP\htdocs\ci4\system\Common.php(1173): CodeIgniter\View\View->render('home', Array, true)
#18 D:\XAMPP\htdocs\ci4\app\Controllers\Page.php(7): view('home', Array)
#19 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#20 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#21 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#22 D:\XAMPP\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#23 D:\XAMPP\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#24 D:\XAMPP\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#25 D:\XAMPP\htdocs\ci4\system\rewrite.php(44): require_once('D:\\XAMPP\\htdocs...')
#26 {main}

Next CodeIgniter\Database\Exceptions\DatabaseException: Unknown database 'sukses' in D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php:246
Stack trace:
#0 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#1 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#2 D:\XAMPP\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#3 D:\XAMPP\htdocs\ci4\system\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#4 D:\XAMPP\htdocs\ci4\system\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
#5 D:\XAMPP\htdocs\ci4\app\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
#6 D:\XAMPP\htdocs\ci4\system\View\Cell.php(307): App\Cells\ArticleLatest->render(NULL)
#7 D:\XAMPP\htdocs\ci4\system\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', Array, 'App\\Cells\\Artic...')
#8 D:\XAMPP\htdocs\ci4\system\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\Artic...', Array, 0, 'AppCellsArticle...')
#9 D:\XAMPP\htdocs\ci4\app\Views\template\footer.php(4): view_cell('App\\Cells\\Artic...', Array)
#10 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#11 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#12 D:\XAMPP\htdocs\ci4\system\View\View.php(475): CodeIgniter\View\View->render('template/footer', NULL, true)
#13 D:\XAMPP\htdocs\ci4\app\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
#14 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#15 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#16 D:\XAMPP\htdocs\ci4\system\Common.php(1173): CodeIgniter\View\View->render('home', Array, true)
#17 D:\XAMPP\htdocs\ci4\app\Controllers\Page.php(7): view('home', Array)
#18 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#19 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#20 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#21 D:\XAMPP\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#22 D:\XAMPP\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#23 D:\XAMPP\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#24 D:\XAMPP\htdocs\ci4\system\rewrite.php(44): require_once('D:\\XAMPP\\htdocs...')
#25 {main}
CRITICAL - 2025-06-20 09:23:01 --> CodeIgniter\Database\Exceptions\DatabaseException: Unable to connect to the database.
Main connection [MySQLi]: Unknown database 'sukses'
[Method: GET, Route: /]
in SYSTEMPATH\Database\BaseConnection.php on line 467.
 1 SYSTEMPATH\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
 2 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 3 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 4 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 5 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 6 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 7 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 8 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
 9 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
10 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
11 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
12 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
13 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
14 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
15 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
16 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
17 APPPATH\Controllers\Page.php(7): view('home', [...])
18 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
19 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
20 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
21 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
22 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
23 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
24 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
DEBUG - 2025-06-20 09:23:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-20 09:23:03 --> Error connecting to the database: mysqli_sql_exception: Unknown database 'sukses' in D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php:201
Stack trace:
#0 D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php(201): mysqli->real_connect('localhost', 'root', Object(SensitiveParameterValue), 'sukses', 3306, '', 0)
#1 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#2 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#3 D:\XAMPP\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 D:\XAMPP\htdocs\ci4\system\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#5 D:\XAMPP\htdocs\ci4\system\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
#6 D:\XAMPP\htdocs\ci4\app\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
#7 D:\XAMPP\htdocs\ci4\system\View\Cell.php(307): App\Cells\ArticleLatest->render(NULL)
#8 D:\XAMPP\htdocs\ci4\system\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', Array, 'App\\Cells\\Artic...')
#9 D:\XAMPP\htdocs\ci4\system\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\Artic...', Array, 0, 'AppCellsArticle...')
#10 D:\XAMPP\htdocs\ci4\app\Views\template\footer.php(4): view_cell('App\\Cells\\Artic...', Array)
#11 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#12 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#13 D:\XAMPP\htdocs\ci4\system\View\View.php(475): CodeIgniter\View\View->render('template/footer', NULL, true)
#14 D:\XAMPP\htdocs\ci4\app\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
#15 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#16 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#17 D:\XAMPP\htdocs\ci4\system\Common.php(1173): CodeIgniter\View\View->render('home', Array, true)
#18 D:\XAMPP\htdocs\ci4\app\Controllers\Page.php(7): view('home', Array)
#19 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#20 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#21 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#22 D:\XAMPP\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#23 D:\XAMPP\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#24 D:\XAMPP\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#25 D:\XAMPP\htdocs\ci4\system\rewrite.php(44): require_once('D:\\XAMPP\\htdocs...')
#26 {main}

Next CodeIgniter\Database\Exceptions\DatabaseException: Unknown database 'sukses' in D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php:246
Stack trace:
#0 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#1 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#2 D:\XAMPP\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#3 D:\XAMPP\htdocs\ci4\system\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#4 D:\XAMPP\htdocs\ci4\system\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
#5 D:\XAMPP\htdocs\ci4\app\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
#6 D:\XAMPP\htdocs\ci4\system\View\Cell.php(307): App\Cells\ArticleLatest->render(NULL)
#7 D:\XAMPP\htdocs\ci4\system\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', Array, 'App\\Cells\\Artic...')
#8 D:\XAMPP\htdocs\ci4\system\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\Artic...', Array, 0, 'AppCellsArticle...')
#9 D:\XAMPP\htdocs\ci4\app\Views\template\footer.php(4): view_cell('App\\Cells\\Artic...', Array)
#10 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#11 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#12 D:\XAMPP\htdocs\ci4\system\View\View.php(475): CodeIgniter\View\View->render('template/footer', NULL, true)
#13 D:\XAMPP\htdocs\ci4\app\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
#14 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#15 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#16 D:\XAMPP\htdocs\ci4\system\Common.php(1173): CodeIgniter\View\View->render('home', Array, true)
#17 D:\XAMPP\htdocs\ci4\app\Controllers\Page.php(7): view('home', Array)
#18 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#19 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#20 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#21 D:\XAMPP\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#22 D:\XAMPP\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#23 D:\XAMPP\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#24 D:\XAMPP\htdocs\ci4\system\rewrite.php(44): require_once('D:\\XAMPP\\htdocs...')
#25 {main}
CRITICAL - 2025-06-20 09:23:03 --> CodeIgniter\Database\Exceptions\DatabaseException: Unable to connect to the database.
Main connection [MySQLi]: Unknown database 'sukses'
[Method: GET, Route: /]
in SYSTEMPATH\Database\BaseConnection.php on line 467.
 1 SYSTEMPATH\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
 2 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 3 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 4 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 5 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 6 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 7 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 8 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
 9 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
10 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
11 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
12 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
13 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
14 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
15 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
16 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
17 APPPATH\Controllers\Page.php(7): view('home', [...])
18 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
19 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
20 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
21 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
22 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
23 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
24 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
DEBUG - 2025-06-20 09:23:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-20 09:23:03 --> Error connecting to the database: mysqli_sql_exception: Unknown database 'sukses' in D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php:201
Stack trace:
#0 D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php(201): mysqli->real_connect('localhost', 'root', Object(SensitiveParameterValue), 'sukses', 3306, '', 0)
#1 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#2 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#3 D:\XAMPP\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 D:\XAMPP\htdocs\ci4\system\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#5 D:\XAMPP\htdocs\ci4\system\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
#6 D:\XAMPP\htdocs\ci4\app\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
#7 D:\XAMPP\htdocs\ci4\system\View\Cell.php(307): App\Cells\ArticleLatest->render(NULL)
#8 D:\XAMPP\htdocs\ci4\system\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', Array, 'App\\Cells\\Artic...')
#9 D:\XAMPP\htdocs\ci4\system\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\Artic...', Array, 0, 'AppCellsArticle...')
#10 D:\XAMPP\htdocs\ci4\app\Views\template\footer.php(4): view_cell('App\\Cells\\Artic...', Array)
#11 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#12 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#13 D:\XAMPP\htdocs\ci4\system\View\View.php(475): CodeIgniter\View\View->render('template/footer', NULL, true)
#14 D:\XAMPP\htdocs\ci4\app\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
#15 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#16 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#17 D:\XAMPP\htdocs\ci4\system\Common.php(1173): CodeIgniter\View\View->render('home', Array, true)
#18 D:\XAMPP\htdocs\ci4\app\Controllers\Page.php(7): view('home', Array)
#19 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#20 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#21 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#22 D:\XAMPP\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#23 D:\XAMPP\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#24 D:\XAMPP\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#25 D:\XAMPP\htdocs\ci4\system\rewrite.php(44): require_once('D:\\XAMPP\\htdocs...')
#26 {main}

Next CodeIgniter\Database\Exceptions\DatabaseException: Unknown database 'sukses' in D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php:246
Stack trace:
#0 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#1 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#2 D:\XAMPP\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#3 D:\XAMPP\htdocs\ci4\system\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#4 D:\XAMPP\htdocs\ci4\system\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
#5 D:\XAMPP\htdocs\ci4\app\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
#6 D:\XAMPP\htdocs\ci4\system\View\Cell.php(307): App\Cells\ArticleLatest->render(NULL)
#7 D:\XAMPP\htdocs\ci4\system\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', Array, 'App\\Cells\\Artic...')
#8 D:\XAMPP\htdocs\ci4\system\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\Artic...', Array, 0, 'AppCellsArticle...')
#9 D:\XAMPP\htdocs\ci4\app\Views\template\footer.php(4): view_cell('App\\Cells\\Artic...', Array)
#10 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#11 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#12 D:\XAMPP\htdocs\ci4\system\View\View.php(475): CodeIgniter\View\View->render('template/footer', NULL, true)
#13 D:\XAMPP\htdocs\ci4\app\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
#14 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#15 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#16 D:\XAMPP\htdocs\ci4\system\Common.php(1173): CodeIgniter\View\View->render('home', Array, true)
#17 D:\XAMPP\htdocs\ci4\app\Controllers\Page.php(7): view('home', Array)
#18 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#19 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#20 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#21 D:\XAMPP\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#22 D:\XAMPP\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#23 D:\XAMPP\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#24 D:\XAMPP\htdocs\ci4\system\rewrite.php(44): require_once('D:\\XAMPP\\htdocs...')
#25 {main}
CRITICAL - 2025-06-20 09:23:03 --> CodeIgniter\Database\Exceptions\DatabaseException: Unable to connect to the database.
Main connection [MySQLi]: Unknown database 'sukses'
[Method: GET, Route: /]
in SYSTEMPATH\Database\BaseConnection.php on line 467.
 1 SYSTEMPATH\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
 2 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 3 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 4 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 5 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 6 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 7 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 8 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
 9 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
10 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
11 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
12 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
13 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
14 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
15 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
16 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
17 APPPATH\Controllers\Page.php(7): view('home', [...])
18 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
19 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
20 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
21 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
22 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
23 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
24 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
DEBUG - 2025-06-20 09:23:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-20 09:23:55 --> mysqli_sql_exception: Table 'sukses.article' doesn't exist in D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php(327): mysqli->query('SELECT *\nFROM `...', 0)
#1 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *\nFROM `...')
#2 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *\nFROM `...')
#3 D:\XAMPP\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 D:\XAMPP\htdocs\ci4\system\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#5 D:\XAMPP\htdocs\ci4\system\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
#6 D:\XAMPP\htdocs\ci4\app\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
#7 D:\XAMPP\htdocs\ci4\system\View\Cell.php(307): App\Cells\ArticleLatest->render(NULL)
#8 D:\XAMPP\htdocs\ci4\system\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', Array, 'App\\Cells\\Artic...')
#9 D:\XAMPP\htdocs\ci4\system\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\Artic...', Array, 0, 'AppCellsArticle...')
#10 D:\XAMPP\htdocs\ci4\app\Views\template\footer.php(4): view_cell('App\\Cells\\Artic...', Array)
#11 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#12 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#13 D:\XAMPP\htdocs\ci4\system\View\View.php(475): CodeIgniter\View\View->render('template/footer', NULL, true)
#14 D:\XAMPP\htdocs\ci4\app\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
#15 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#16 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#17 D:\XAMPP\htdocs\ci4\system\Common.php(1173): CodeIgniter\View\View->render('home', Array, true)
#18 D:\XAMPP\htdocs\ci4\app\Controllers\Page.php(7): view('home', Array)
#19 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#20 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#21 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#22 D:\XAMPP\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#23 D:\XAMPP\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#24 D:\XAMPP\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#25 D:\XAMPP\htdocs\ci4\system\rewrite.php(44): require_once('D:\\XAMPP\\htdocs...')
#26 {main}
CRITICAL - 2025-06-20 09:23:55 --> CodeIgniter\Database\Exceptions\DatabaseException: Table 'sukses.article' doesn't exist
[Method: GET, Route: /]
in SYSTEMPATH\Database\BaseConnection.php on line 692.
 1 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 2 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 3 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 4 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 5 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 6 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 7 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
 8 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
 9 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
10 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
11 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
12 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
13 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
14 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
15 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
16 APPPATH\Controllers\Page.php(7): view('home', [...])
17 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
18 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
19 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
20 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
21 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
22 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
23 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
CRITICAL - 2025-06-20 09:23:55 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Table 'sukses.article' doesn't exist
in SYSTEMPATH\Database\MySQLi\Connection.php on line 332.
 1 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 2 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 3 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 4 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 5 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 6 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 7 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 8 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 9 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
10 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
11 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
12 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
13 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
14 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
15 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
16 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
17 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
18 APPPATH\Controllers\Page.php(7): view('home', [...])
19 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
20 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
21 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
22 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
23 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
24 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
25 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
CRITICAL - 2025-06-20 09:23:55 --> [Caused by] mysqli_sql_exception: Table 'sukses.article' doesn't exist
in SYSTEMPATH\Database\MySQLi\Connection.php on line 327.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(327): mysqli->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 3 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 4 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 5 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 6 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 7 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 8 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 9 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
10 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
11 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
12 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
13 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
14 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
15 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
16 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
17 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
18 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
19 APPPATH\Controllers\Page.php(7): view('home', [...])
20 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
21 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
22 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
23 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
24 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
25 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
26 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
DEBUG - 2025-06-20 09:23:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-20 09:23:57 --> mysqli_sql_exception: Table 'sukses.article' doesn't exist in D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php(327): mysqli->query('SELECT *\nFROM `...', 0)
#1 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *\nFROM `...')
#2 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *\nFROM `...')
#3 D:\XAMPP\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 D:\XAMPP\htdocs\ci4\system\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#5 D:\XAMPP\htdocs\ci4\system\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
#6 D:\XAMPP\htdocs\ci4\app\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
#7 D:\XAMPP\htdocs\ci4\system\View\Cell.php(307): App\Cells\ArticleLatest->render(NULL)
#8 D:\XAMPP\htdocs\ci4\system\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', Array, 'App\\Cells\\Artic...')
#9 D:\XAMPP\htdocs\ci4\system\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\Artic...', Array, 0, 'AppCellsArticle...')
#10 D:\XAMPP\htdocs\ci4\app\Views\template\footer.php(4): view_cell('App\\Cells\\Artic...', Array)
#11 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#12 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#13 D:\XAMPP\htdocs\ci4\system\View\View.php(475): CodeIgniter\View\View->render('template/footer', NULL, true)
#14 D:\XAMPP\htdocs\ci4\app\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
#15 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#16 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#17 D:\XAMPP\htdocs\ci4\system\Common.php(1173): CodeIgniter\View\View->render('home', Array, true)
#18 D:\XAMPP\htdocs\ci4\app\Controllers\Page.php(7): view('home', Array)
#19 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#20 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#21 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#22 D:\XAMPP\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#23 D:\XAMPP\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#24 D:\XAMPP\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#25 D:\XAMPP\htdocs\ci4\system\rewrite.php(44): require_once('D:\\XAMPP\\htdocs...')
#26 {main}
CRITICAL - 2025-06-20 09:23:57 --> CodeIgniter\Database\Exceptions\DatabaseException: Table 'sukses.article' doesn't exist
[Method: GET, Route: /]
in SYSTEMPATH\Database\BaseConnection.php on line 692.
 1 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 2 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 3 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 4 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 5 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 6 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 7 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
 8 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
 9 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
10 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
11 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
12 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
13 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
14 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
15 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
16 APPPATH\Controllers\Page.php(7): view('home', [...])
17 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
18 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
19 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
20 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
21 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
22 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
23 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
CRITICAL - 2025-06-20 09:23:57 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Table 'sukses.article' doesn't exist
in SYSTEMPATH\Database\MySQLi\Connection.php on line 332.
 1 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 2 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 3 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 4 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 5 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 6 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 7 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 8 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 9 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
10 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
11 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
12 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
13 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
14 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
15 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
16 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
17 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
18 APPPATH\Controllers\Page.php(7): view('home', [...])
19 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
20 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
21 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
22 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
23 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
24 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
25 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
CRITICAL - 2025-06-20 09:23:57 --> [Caused by] mysqli_sql_exception: Table 'sukses.article' doesn't exist
in SYSTEMPATH\Database\MySQLi\Connection.php on line 327.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(327): mysqli->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 3 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 4 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 5 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 6 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 7 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 8 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 9 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
10 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
11 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
12 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
13 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
14 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
15 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
16 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
17 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
18 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
19 APPPATH\Controllers\Page.php(7): view('home', [...])
20 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
21 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
22 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
23 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
24 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
25 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
26 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
DEBUG - 2025-06-20 10:25:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-20 10:25:08 --> mysqli_sql_exception: Table 'sukses.article' doesn't exist in D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php(327): mysqli->query('SELECT *\nFROM `...', 0)
#1 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *\nFROM `...')
#2 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *\nFROM `...')
#3 D:\XAMPP\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 D:\XAMPP\htdocs\ci4\system\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#5 D:\XAMPP\htdocs\ci4\system\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
#6 D:\XAMPP\htdocs\ci4\app\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
#7 D:\XAMPP\htdocs\ci4\system\View\Cell.php(307): App\Cells\ArticleLatest->render(NULL)
#8 D:\XAMPP\htdocs\ci4\system\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', Array, 'App\\Cells\\Artic...')
#9 D:\XAMPP\htdocs\ci4\system\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\Artic...', Array, 0, 'AppCellsArticle...')
#10 D:\XAMPP\htdocs\ci4\app\Views\template\footer.php(4): view_cell('App\\Cells\\Artic...', Array)
#11 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#12 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#13 D:\XAMPP\htdocs\ci4\system\View\View.php(475): CodeIgniter\View\View->render('template/footer', NULL, true)
#14 D:\XAMPP\htdocs\ci4\app\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
#15 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#16 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#17 D:\XAMPP\htdocs\ci4\system\Common.php(1173): CodeIgniter\View\View->render('home', Array, true)
#18 D:\XAMPP\htdocs\ci4\app\Controllers\Page.php(7): view('home', Array)
#19 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#20 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#21 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#22 D:\XAMPP\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#23 D:\XAMPP\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#24 D:\XAMPP\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#25 D:\XAMPP\htdocs\ci4\system\rewrite.php(44): require_once('D:\\XAMPP\\htdocs...')
#26 {main}
CRITICAL - 2025-06-20 10:25:08 --> CodeIgniter\Database\Exceptions\DatabaseException: Table 'sukses.article' doesn't exist
[Method: GET, Route: /]
in SYSTEMPATH\Database\BaseConnection.php on line 692.
 1 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 2 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 3 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 4 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 5 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 6 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 7 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
 8 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
 9 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
10 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
11 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
12 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
13 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
14 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
15 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
16 APPPATH\Controllers\Page.php(7): view('home', [...])
17 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
18 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
19 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
20 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
21 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
22 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
23 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
CRITICAL - 2025-06-20 10:25:08 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Table 'sukses.article' doesn't exist
in SYSTEMPATH\Database\MySQLi\Connection.php on line 332.
 1 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 2 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 3 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 4 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 5 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 6 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 7 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 8 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 9 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
10 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
11 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
12 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
13 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
14 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
15 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
16 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
17 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
18 APPPATH\Controllers\Page.php(7): view('home', [...])
19 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
20 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
21 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
22 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
23 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
24 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
25 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
CRITICAL - 2025-06-20 10:25:08 --> [Caused by] mysqli_sql_exception: Table 'sukses.article' doesn't exist
in SYSTEMPATH\Database\MySQLi\Connection.php on line 327.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(327): mysqli->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 3 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 4 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 5 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 6 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 7 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 8 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 9 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
10 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
11 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
12 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
13 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
14 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
15 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
16 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
17 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
18 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
19 APPPATH\Controllers\Page.php(7): view('home', [...])
20 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
21 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
22 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
23 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
24 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
25 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
26 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
DEBUG - 2025-06-20 10:27:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-20 10:27:56 --> mysqli_sql_exception: Table 'sukses.article' doesn't exist in engine in D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php(327): mysqli->query('SELECT *\nFROM `...', 0)
#1 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *\nFROM `...')
#2 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *\nFROM `...')
#3 D:\XAMPP\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 D:\XAMPP\htdocs\ci4\system\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#5 D:\XAMPP\htdocs\ci4\system\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
#6 D:\XAMPP\htdocs\ci4\app\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
#7 D:\XAMPP\htdocs\ci4\system\View\Cell.php(307): App\Cells\ArticleLatest->render(NULL)
#8 D:\XAMPP\htdocs\ci4\system\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', Array, 'App\\Cells\\Artic...')
#9 D:\XAMPP\htdocs\ci4\system\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\Artic...', Array, 0, 'AppCellsArticle...')
#10 D:\XAMPP\htdocs\ci4\app\Views\template\footer.php(4): view_cell('App\\Cells\\Artic...', Array)
#11 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#12 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#13 D:\XAMPP\htdocs\ci4\system\View\View.php(475): CodeIgniter\View\View->render('template/footer', NULL, true)
#14 D:\XAMPP\htdocs\ci4\app\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
#15 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#16 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#17 D:\XAMPP\htdocs\ci4\system\Common.php(1173): CodeIgniter\View\View->render('home', Array, true)
#18 D:\XAMPP\htdocs\ci4\app\Controllers\Page.php(7): view('home', Array)
#19 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#20 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#21 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#22 D:\XAMPP\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#23 D:\XAMPP\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#24 D:\XAMPP\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#25 D:\XAMPP\htdocs\ci4\system\rewrite.php(44): require_once('D:\\XAMPP\\htdocs...')
#26 {main}
CRITICAL - 2025-06-20 10:27:56 --> CodeIgniter\Database\Exceptions\DatabaseException: Table 'sukses.article' doesn't exist in engine
[Method: GET, Route: /]
in SYSTEMPATH\Database\BaseConnection.php on line 692.
 1 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 2 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 3 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 4 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 5 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 6 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 7 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
 8 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
 9 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
10 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
11 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
12 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
13 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
14 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
15 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
16 APPPATH\Controllers\Page.php(7): view('home', [...])
17 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
18 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
19 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
20 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
21 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
22 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
23 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
CRITICAL - 2025-06-20 10:27:56 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Table 'sukses.article' doesn't exist in engine
in SYSTEMPATH\Database\MySQLi\Connection.php on line 332.
 1 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 2 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 3 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 4 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 5 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 6 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 7 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 8 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 9 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
10 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
11 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
12 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
13 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
14 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
15 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
16 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
17 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
18 APPPATH\Controllers\Page.php(7): view('home', [...])
19 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
20 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
21 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
22 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
23 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
24 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
25 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
CRITICAL - 2025-06-20 10:27:56 --> [Caused by] mysqli_sql_exception: Table 'sukses.article' doesn't exist in engine
in SYSTEMPATH\Database\MySQLi\Connection.php on line 327.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(327): mysqli->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 3 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 4 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 5 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 6 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 7 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 8 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 9 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
10 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
11 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
12 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
13 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
14 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
15 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
16 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
17 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
18 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
19 APPPATH\Controllers\Page.php(7): view('home', [...])
20 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
21 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
22 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
23 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
24 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
25 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
26 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
DEBUG - 2025-06-20 10:27:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-20 10:27:59 --> mysqli_sql_exception: Table 'sukses.article' doesn't exist in engine in D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php(327): mysqli->query('SELECT *\nFROM `...', 0)
#1 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *\nFROM `...')
#2 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *\nFROM `...')
#3 D:\XAMPP\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 D:\XAMPP\htdocs\ci4\system\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#5 D:\XAMPP\htdocs\ci4\system\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
#6 D:\XAMPP\htdocs\ci4\app\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
#7 D:\XAMPP\htdocs\ci4\system\View\Cell.php(307): App\Cells\ArticleLatest->render(NULL)
#8 D:\XAMPP\htdocs\ci4\system\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', Array, 'App\\Cells\\Artic...')
#9 D:\XAMPP\htdocs\ci4\system\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\Artic...', Array, 0, 'AppCellsArticle...')
#10 D:\XAMPP\htdocs\ci4\app\Views\template\footer.php(4): view_cell('App\\Cells\\Artic...', Array)
#11 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#12 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#13 D:\XAMPP\htdocs\ci4\system\View\View.php(475): CodeIgniter\View\View->render('template/footer', NULL, true)
#14 D:\XAMPP\htdocs\ci4\app\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
#15 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#16 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#17 D:\XAMPP\htdocs\ci4\system\Common.php(1173): CodeIgniter\View\View->render('home', Array, true)
#18 D:\XAMPP\htdocs\ci4\app\Controllers\Page.php(7): view('home', Array)
#19 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#20 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#21 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#22 D:\XAMPP\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#23 D:\XAMPP\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#24 D:\XAMPP\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#25 D:\XAMPP\htdocs\ci4\system\rewrite.php(44): require_once('D:\\XAMPP\\htdocs...')
#26 {main}
CRITICAL - 2025-06-20 10:27:59 --> CodeIgniter\Database\Exceptions\DatabaseException: Table 'sukses.article' doesn't exist in engine
[Method: GET, Route: /]
in SYSTEMPATH\Database\BaseConnection.php on line 692.
 1 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 2 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 3 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 4 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 5 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 6 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 7 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
 8 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
 9 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
10 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
11 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
12 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
13 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
14 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
15 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
16 APPPATH\Controllers\Page.php(7): view('home', [...])
17 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
18 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
19 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
20 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
21 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
22 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
23 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
CRITICAL - 2025-06-20 10:27:59 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Table 'sukses.article' doesn't exist in engine
in SYSTEMPATH\Database\MySQLi\Connection.php on line 332.
 1 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 2 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 3 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 4 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 5 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 6 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 7 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 8 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 9 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
10 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
11 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
12 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
13 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
14 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
15 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
16 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
17 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
18 APPPATH\Controllers\Page.php(7): view('home', [...])
19 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
20 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
21 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
22 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
23 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
24 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
25 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
CRITICAL - 2025-06-20 10:27:59 --> [Caused by] mysqli_sql_exception: Table 'sukses.article' doesn't exist in engine
in SYSTEMPATH\Database\MySQLi\Connection.php on line 327.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(327): mysqli->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 3 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 4 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 5 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 6 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 7 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 8 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 9 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
10 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
11 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
12 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
13 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
14 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
15 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
16 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
17 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
18 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
19 APPPATH\Controllers\Page.php(7): view('home', [...])
20 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
21 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
22 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
23 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
24 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
25 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
26 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
DEBUG - 2025-06-20 10:35:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-20 10:35:22 --> mysqli_sql_exception: Unknown column 'created_at' in 'order clause' in D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php(327): mysqli->query('SELECT *\nFROM `...', 0)
#1 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *\nFROM `...')
#2 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *\nFROM `...')
#3 D:\XAMPP\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 D:\XAMPP\htdocs\ci4\system\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#5 D:\XAMPP\htdocs\ci4\system\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
#6 D:\XAMPP\htdocs\ci4\app\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
#7 D:\XAMPP\htdocs\ci4\system\View\Cell.php(307): App\Cells\ArticleLatest->render(NULL)
#8 D:\XAMPP\htdocs\ci4\system\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', Array, 'App\\Cells\\Artic...')
#9 D:\XAMPP\htdocs\ci4\system\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\Artic...', Array, 0, 'AppCellsArticle...')
#10 D:\XAMPP\htdocs\ci4\app\Views\template\footer.php(4): view_cell('App\\Cells\\Artic...', Array)
#11 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#12 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#13 D:\XAMPP\htdocs\ci4\system\View\View.php(475): CodeIgniter\View\View->render('template/footer', NULL, true)
#14 D:\XAMPP\htdocs\ci4\app\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
#15 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#16 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#17 D:\XAMPP\htdocs\ci4\system\Common.php(1173): CodeIgniter\View\View->render('home', Array, true)
#18 D:\XAMPP\htdocs\ci4\app\Controllers\Page.php(7): view('home', Array)
#19 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#20 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#21 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#22 D:\XAMPP\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#23 D:\XAMPP\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#24 D:\XAMPP\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#25 D:\XAMPP\htdocs\ci4\system\rewrite.php(44): require_once('D:\\XAMPP\\htdocs...')
#26 {main}
CRITICAL - 2025-06-20 10:35:22 --> CodeIgniter\Database\Exceptions\DatabaseException: Unknown column 'created_at' in 'order clause'
[Method: GET, Route: /]
in SYSTEMPATH\Database\BaseConnection.php on line 692.
 1 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `artikel`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 2 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 3 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 4 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 5 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 6 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 7 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
 8 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
 9 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
10 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
11 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
12 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
13 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
14 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
15 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
16 APPPATH\Controllers\Page.php(7): view('home', [...])
17 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
18 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
19 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
20 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
21 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
22 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
23 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
CRITICAL - 2025-06-20 10:35:22 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Unknown column 'created_at' in 'order clause'
in SYSTEMPATH\Database\MySQLi\Connection.php on line 332.
 1 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `artikel`
ORDER BY `created_at` DESC
 LIMIT 5')
 2 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `artikel`
ORDER BY `created_at` DESC
 LIMIT 5')
 3 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `artikel`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 4 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 5 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 6 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 7 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 8 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 9 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
10 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
11 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
12 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
13 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
14 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
15 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
16 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
17 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
18 APPPATH\Controllers\Page.php(7): view('home', [...])
19 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
20 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
21 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
22 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
23 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
24 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
25 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
CRITICAL - 2025-06-20 10:35:22 --> [Caused by] mysqli_sql_exception: Unknown column 'created_at' in 'order clause'
in SYSTEMPATH\Database\MySQLi\Connection.php on line 327.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(327): mysqli->query('SELECT *
FROM `artikel`
ORDER BY `created_at` DESC
 LIMIT 5', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `artikel`
ORDER BY `created_at` DESC
 LIMIT 5')
 3 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `artikel`
ORDER BY `created_at` DESC
 LIMIT 5')
 4 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `artikel`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 5 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 6 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 7 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 8 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 9 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
10 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
11 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
12 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
13 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
14 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
15 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
16 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
17 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
18 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
19 APPPATH\Controllers\Page.php(7): view('home', [...])
20 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
21 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
22 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
23 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
24 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
25 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
26 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
DEBUG - 2025-06-20 10:35:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-20 10:35:26 --> mysqli_sql_exception: Unknown column 'created_at' in 'order clause' in D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php(327): mysqli->query('SELECT *\nFROM `...', 0)
#1 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *\nFROM `...')
#2 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *\nFROM `...')
#3 D:\XAMPP\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 D:\XAMPP\htdocs\ci4\system\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#5 D:\XAMPP\htdocs\ci4\system\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
#6 D:\XAMPP\htdocs\ci4\app\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
#7 D:\XAMPP\htdocs\ci4\system\View\Cell.php(307): App\Cells\ArticleLatest->render(NULL)
#8 D:\XAMPP\htdocs\ci4\system\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', Array, 'App\\Cells\\Artic...')
#9 D:\XAMPP\htdocs\ci4\system\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\Artic...', Array, 0, 'AppCellsArticle...')
#10 D:\XAMPP\htdocs\ci4\app\Views\template\footer.php(4): view_cell('App\\Cells\\Artic...', Array)
#11 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#12 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#13 D:\XAMPP\htdocs\ci4\system\View\View.php(475): CodeIgniter\View\View->render('template/footer', NULL, true)
#14 D:\XAMPP\htdocs\ci4\app\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
#15 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#16 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#17 D:\XAMPP\htdocs\ci4\system\Common.php(1173): CodeIgniter\View\View->render('home', Array, true)
#18 D:\XAMPP\htdocs\ci4\app\Controllers\Page.php(7): view('home', Array)
#19 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#20 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#21 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#22 D:\XAMPP\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#23 D:\XAMPP\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#24 D:\XAMPP\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#25 D:\XAMPP\htdocs\ci4\system\rewrite.php(44): require_once('D:\\XAMPP\\htdocs...')
#26 {main}
CRITICAL - 2025-06-20 10:35:26 --> CodeIgniter\Database\Exceptions\DatabaseException: Unknown column 'created_at' in 'order clause'
[Method: GET, Route: /]
in SYSTEMPATH\Database\BaseConnection.php on line 692.
 1 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `artikel`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 2 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 3 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 4 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 5 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 6 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 7 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
 8 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
 9 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
10 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
11 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
12 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
13 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
14 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
15 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
16 APPPATH\Controllers\Page.php(7): view('home', [...])
17 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
18 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
19 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
20 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
21 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
22 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
23 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
CRITICAL - 2025-06-20 10:35:26 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Unknown column 'created_at' in 'order clause'
in SYSTEMPATH\Database\MySQLi\Connection.php on line 332.
 1 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `artikel`
ORDER BY `created_at` DESC
 LIMIT 5')
 2 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `artikel`
ORDER BY `created_at` DESC
 LIMIT 5')
 3 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `artikel`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 4 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 5 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 6 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 7 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 8 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 9 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
10 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
11 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
12 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
13 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
14 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
15 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
16 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
17 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
18 APPPATH\Controllers\Page.php(7): view('home', [...])
19 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
20 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
21 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
22 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
23 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
24 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
25 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
CRITICAL - 2025-06-20 10:35:26 --> [Caused by] mysqli_sql_exception: Unknown column 'created_at' in 'order clause'
in SYSTEMPATH\Database\MySQLi\Connection.php on line 327.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(327): mysqli->query('SELECT *
FROM `artikel`
ORDER BY `created_at` DESC
 LIMIT 5', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `artikel`
ORDER BY `created_at` DESC
 LIMIT 5')
 3 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `artikel`
ORDER BY `created_at` DESC
 LIMIT 5')
 4 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `artikel`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 5 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 6 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 7 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 8 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 9 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
10 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
11 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
12 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
13 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
14 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
15 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
16 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
17 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
18 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
19 APPPATH\Controllers\Page.php(7): view('home', [...])
20 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
21 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
22 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
23 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
24 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
25 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
26 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
DEBUG - 2025-06-20 10:36:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-20 10:36:23 --> mysqli_sql_exception: Table 'sukses.article' doesn't exist in engine in D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php(327): mysqli->query('SELECT *\nFROM `...', 0)
#1 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *\nFROM `...')
#2 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *\nFROM `...')
#3 D:\XAMPP\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 D:\XAMPP\htdocs\ci4\system\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#5 D:\XAMPP\htdocs\ci4\system\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
#6 D:\XAMPP\htdocs\ci4\app\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
#7 D:\XAMPP\htdocs\ci4\system\View\Cell.php(307): App\Cells\ArticleLatest->render(NULL)
#8 D:\XAMPP\htdocs\ci4\system\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', Array, 'App\\Cells\\Artic...')
#9 D:\XAMPP\htdocs\ci4\system\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\Artic...', Array, 0, 'AppCellsArticle...')
#10 D:\XAMPP\htdocs\ci4\app\Views\template\footer.php(4): view_cell('App\\Cells\\Artic...', Array)
#11 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#12 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#13 D:\XAMPP\htdocs\ci4\system\View\View.php(475): CodeIgniter\View\View->render('template/footer', NULL, true)
#14 D:\XAMPP\htdocs\ci4\app\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
#15 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#16 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#17 D:\XAMPP\htdocs\ci4\system\Common.php(1173): CodeIgniter\View\View->render('home', Array, true)
#18 D:\XAMPP\htdocs\ci4\app\Controllers\Page.php(7): view('home', Array)
#19 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#20 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#21 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#22 D:\XAMPP\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#23 D:\XAMPP\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#24 D:\XAMPP\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#25 D:\XAMPP\htdocs\ci4\system\rewrite.php(44): require_once('D:\\XAMPP\\htdocs...')
#26 {main}
CRITICAL - 2025-06-20 10:36:23 --> CodeIgniter\Database\Exceptions\DatabaseException: Table 'sukses.article' doesn't exist in engine
[Method: GET, Route: /]
in SYSTEMPATH\Database\BaseConnection.php on line 692.
 1 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 2 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 3 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 4 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 5 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 6 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 7 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
 8 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
 9 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
10 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
11 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
12 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
13 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
14 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
15 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
16 APPPATH\Controllers\Page.php(7): view('home', [...])
17 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
18 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
19 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
20 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
21 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
22 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
23 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
CRITICAL - 2025-06-20 10:36:23 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Table 'sukses.article' doesn't exist in engine
in SYSTEMPATH\Database\MySQLi\Connection.php on line 332.
 1 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 2 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 3 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 4 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 5 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 6 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 7 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 8 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 9 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
10 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
11 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
12 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
13 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
14 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
15 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
16 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
17 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
18 APPPATH\Controllers\Page.php(7): view('home', [...])
19 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
20 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
21 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
22 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
23 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
24 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
25 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
CRITICAL - 2025-06-20 10:36:23 --> [Caused by] mysqli_sql_exception: Table 'sukses.article' doesn't exist in engine
in SYSTEMPATH\Database\MySQLi\Connection.php on line 327.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(327): mysqli->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 3 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 4 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 5 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 6 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 7 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 8 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 9 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
10 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
11 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
12 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
13 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
14 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
15 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
16 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
17 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
18 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
19 APPPATH\Controllers\Page.php(7): view('home', [...])
20 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
21 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
22 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
23 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
24 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
25 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
26 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
