<svg width="220" height="55" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="premiumGradient3" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background with rounded corners -->
  <rect x="0" y="0" width="220" height="55" rx="18" ry="18" fill="url(#premiumGradient3)" />
  
  <!-- Info/News icon with modern design -->
  <g transform="translate(20, 12)">
    <!-- Monitor/screen -->
    <rect x="0" y="0" width="26" height="18" rx="2" fill="white" opacity="0.95"/>
    <rect x="2" y="2" width="22" height="14" rx="1" fill="#667eea" opacity="0.15"/>
    
    <!-- Screen content - news layout -->
    <rect x="4" y="4" width="8" height="4" rx="1" fill="white"/>
    <rect x="13" y="4" width="7" height="1" fill="rgba(255,255,255,0.9)"/>
    <rect x="13" y="6" width="5" fill="rgba(255,255,255,0.7)"/>
    
    <rect x="4" y="10" width="16" height="0.8" fill="rgba(255,255,255,0.8)"/>
    <rect x="4" y="12" width="12" height="0.8" fill="rgba(255,255,255,0.8)"/>
    <rect x="4" y="14" width="14" height="0.8" fill="rgba(255,255,255,0.8)"/>
    
    <!-- Signal dots -->
    <circle cx="22" cy="6" r="1" fill="white"/>
    <circle cx="19" cy="6" r="0.8" fill="rgba(255,255,255,0.7)"/>
    <circle cx="16" cy="6" r="0.6" fill="rgba(255,255,255,0.5)"/>
  </g>
  
  <!-- Logo text -->
  <text x="130" y="36" font-family="Arial, sans-serif" font-size="18" font-weight="700" 
        text-anchor="middle" fill="white" letter-spacing="1.5px">INFO CENTER</text>
</svg>
