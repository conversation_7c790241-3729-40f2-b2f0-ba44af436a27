# Deployment Guide - setiawanarticle.my.id

## 📁 Struktur File untuk Hosting

```
public_html/
├── .htaccess                 # URL rewriting untuk CI4
├── index.php                 # Entry point CI4
├── gambar/                   # Folder upload gambar artikel
│   ├── .htaccess            # CORS dan caching untuk gambar
│   └── *.jpg, *.png, *.gif  # File gambar artikel
├── vue/                      # Vue.js frontend
│   ├── .htaccess            # CORS dan direct access
│   ├── index.html           # Vue.js app
│   └── assets/
│       ├── css/
│       └── js/
│           └── app.js       # API endpoint sudah diupdate
└── ci4/                     # CodeIgniter 4 backend
    ├── .env                 # Production config
    ├── app/
    ├── system/
    └── ...
```

## 🔧 Konfigurasi yang Sudah Diatur

### 1. File .htaccess (Root)
- URL rewriting untuk CodeIgniter 4
- Skip rewrite untuk folder `/vue/`
- Authorization header support

### 2. File index.php (Root)
- Entry point untuk CodeIgniter 4
- Path ke `ci4/app/Config/Paths.php`

### 3. File .env (ci4/.env)
- Environment: `production`
- Base URL: `https://setiawanarticle.my.id/`
- Force HTTPS: `true`
- Database config:
  - Host: `localhost`
  - Database: `setiawan_setiawan`
  - Username: `setiawan_setiawan`
  - Password: `EDHNJ5b7fVjCRbnpqXhm`

### 4. Folder Gambar
- Folder `gambar/` di root untuk upload artikel
- Path gambar: `https://setiawanarticle.my.id/gambar/filename.jpg`
- CORS dan caching sudah dikonfigurasi

### 5. Vue.js Configuration
- API URL diupdate ke: `https://setiawanarticle.my.id`
- Image URL diupdate ke: `https://setiawanarticle.my.id/gambar/`
- Folder `vue/` bisa diakses langsung via `/vue/`
- CORS sudah dikonfigurasi

### 6. CodeIgniter 4 Config
- `indexPage` = '' (untuk clean URLs)
- CORS origins include production domain
- CORS filter aktif untuk API endpoints

## 🚀 Cara Upload ke Hosting

1. **Upload semua file** ke folder `public_html/` di hosting
2. **Pastikan struktur folder** sesuai dengan di atas
3. **Set permission** folder `ci4/writable/` ke 755 atau 777
4. **Import database** menggunakan file SQL yang ada
5. **Test akses**:
   - Main site: `https://setiawanarticle.my.id/`
   - Vue.js app: `https://setiawanarticle.my.id/vue/`
   - API test: `https://setiawanarticle.my.id/post`

## 🔍 Troubleshooting

### Jika CI4 error:
- Cek permission folder `ci4/writable/`
- Cek file `.env` sudah benar
- Cek database connection

### Jika Vue.js tidak bisa akses API:
- Cek CORS configuration
- Cek API URL di `vue/assets/js/app.js`
- Cek network tab di browser developer tools

### Jika URL tidak clean:
- Cek file `.htaccess` di root
- Pastikan mod_rewrite aktif di hosting
- Cek `indexPage` di `ci4/app/Config/App.php`

## 📝 Notes
- Google OAuth masih menggunakan config development
- Untuk production, update Google OAuth redirect URI
- Backup database sebelum import ke hosting
