<!DOCTYPE html>
<html>
<head>
    <title>Debug Images Test</title>
    <style>
        .test-image { max-width: 200px; margin: 10px; border: 1px solid #ccc; }
        .error { color: red; }
        .success { color: green; }
    </style>
</head>
<body>
    <h1>Debug Images Test</h1>
    
    <h2>Test 1: Direct Image Access</h2>
    <div id="direct-test">
        <p>Testing: https://setiawanarticle.my.id/gambar/1751372799_2c67aa2de1b30ab0ffef.jpg</p>
        <img src="https://setiawanarticle.my.id/gambar/1751372799_2c67aa2de1b30ab0ffef.jpg" 
             class="test-image" 
             onload="showSuccess('direct-result', 'Gambar berhasil dimuat!')"
             onerror="showError('direct-result', 'Gambar gagal dimuat!')">
        <div id="direct-result"></div>
    </div>
    
    <h2>Test 2: All Images from Database</h2>
    <div id="all-images">
        <!-- Images will be loaded here -->
    </div>
    
    <h2>Test 3: Path Variations</h2>
    <div id="path-tests">
        <h3>Path 1: /gambar/ (should work)</h3>
        <img src="https://setiawanarticle.my.id/gambar/1751372799_2c67aa2de1b30ab0ffef.jpg" 
             class="test-image" 
             onload="showSuccess('path1-result', 'Path 1 OK')"
             onerror="showError('path1-result', 'Path 1 FAIL')">
        <div id="path1-result"></div>
        
        <h3>Path 2: /ci4/public/gambar/ (old path)</h3>
        <img src="https://setiawanarticle.my.id/ci4/public/gambar/1751372799_2c67aa2de1b30ab0ffef.jpg" 
             class="test-image" 
             onload="showSuccess('path2-result', 'Path 2 OK')"
             onerror="showError('path2-result', 'Path 2 FAIL')">
        <div id="path2-result"></div>
    </div>
    
    <script>
        function showSuccess(elementId, message) {
            document.getElementById(elementId).innerHTML = '<span class="success">✅ ' + message + '</span>';
        }
        
        function showError(elementId, message) {
            document.getElementById(elementId).innerHTML = '<span class="error">❌ ' + message + '</span>';
        }
        
        // Test all images from the gambar folder
        const imageFiles = [
            '1751372799_2c67aa2de1b30ab0ffef.jpg',
            '1751374232_4983cf9fd36b454d7e27.jpg',
            '1751375383_a9938257c156abe4361e.jpg',
            '1751376747_3ab133a7239b6a858535.jpg',
            '1751398032_80836246de6698ac1a78.jpg',
            '1751398299_aa3b4b1e6a1a10c3ff2b.jpg',
            '1751398712_fc0608703f43780645d1.jpg',
            '1751398767_a00c4d0b27611646c4df.jpg',
            '1751398993_92d738113909955475aa.jpg'
        ];
        
        const allImagesDiv = document.getElementById('all-images');
        
        imageFiles.forEach((filename, index) => {
            const div = document.createElement('div');
            div.innerHTML = `
                <h4>Image ${index + 1}: ${filename}</h4>
                <img src="https://setiawanarticle.my.id/gambar/${filename}" 
                     class="test-image" 
                     onload="showSuccess('result-${index}', 'Loaded successfully')"
                     onerror="showError('result-${index}', 'Failed to load')">
                <div id="result-${index}"></div>
            `;
            allImagesDiv.appendChild(div);
        });
    </script>
</body>
</html>
