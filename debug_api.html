<!DOCTYPE html>
<html>
<head>
    <title>Debug API Test</title>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
</head>
<body>
    <h1>Debug API Test</h1>
    <div id="results"></div>
    
    <script>
        const apiUrl = 'https://setiawanarticle.my.id';
        const results = document.getElementById('results');
        
        function log(message) {
            results.innerHTML += '<p>' + message + '</p>';
            console.log(message);
        }
        
        // Test 1: Basic API connectivity
        log('🔍 Testing API connectivity...');
        axios.get(apiUrl + '/post')
            .then(response => {
                log('✅ API GET /post berhasil!');
                log('Data: ' + JSON.stringify(response.data, null, 2));
            })
            .catch(error => {
                log('❌ API GET /post gagal!');
                log('Error: ' + error.message);
                if (error.response) {
                    log('Status: ' + error.response.status);
                    log('Response: ' + JSON.stringify(error.response.data, null, 2));
                }
            });
            
        // Test 2: CORS headers
        log('🔍 Testing CORS...');
        fetch(apiUrl + '/post', {
            method: 'GET',
            headers: {
                'Origin': 'https://setiawanarticle.my.id'
            }
        })
        .then(response => {
            log('✅ CORS test berhasil!');
            log('CORS Headers:');
            response.headers.forEach((value, key) => {
                if (key.toLowerCase().includes('access-control')) {
                    log(`  ${key}: ${value}`);
                }
            });
        })
        .catch(error => {
            log('❌ CORS test gagal: ' + error.message);
        });
        
        // Test 3: Image accessibility
        log('🔍 Testing image access...');
        const testImg = new Image();
        testImg.onload = function() {
            log('✅ Gambar bisa diakses!');
        };
        testImg.onerror = function() {
            log('❌ Gambar tidak bisa diakses!');
        };
        testImg.src = apiUrl + '/gambar/1751372799_2c67aa2de1b30ab0ffef.jpg';
        
        // Test 4: POST request (simulate form submit)
        log('🔍 Testing POST request...');
        const formData = new FormData();
        formData.append('judul', 'Test Article');
        formData.append('isi', 'Test content');
        formData.append('status', '1');
        formData.append('id_kategori', '1');
        
        axios.post(apiUrl + '/post', formData, {
            headers: {
                'Content-Type': 'multipart/form-data'
            }
        })
        .then(response => {
            log('✅ POST request berhasil!');
            log('Response: ' + JSON.stringify(response.data, null, 2));
        })
        .catch(error => {
            log('❌ POST request gagal!');
            log('Error: ' + error.message);
            if (error.response) {
                log('Status: ' + error.response.status);
                log('Response: ' + JSON.stringify(error.response.data, null, 2));
            }
        });
    </script>
</body>
</html>
