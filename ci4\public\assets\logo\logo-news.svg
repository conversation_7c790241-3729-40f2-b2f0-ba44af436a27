<svg width="200" height="50" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="newsGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background rounded rectangle -->
  <rect x="0" y="0" width="200" height="50" rx="8" ry="8" fill="url(#newsGradient)" />
  
  <!-- News/Document icon -->
  <g fill="white" transform="translate(12, 10)">
    <!-- Multiple papers effect -->
    <rect x="2" y="2" width="18" height="24" rx="2" fill="rgba(255,255,255,0.3)"/>
    <rect x="1" y="1" width="18" height="24" rx="2" fill="rgba(255,255,255,0.5)"/>
    <rect x="0" y="0" width="18" height="24" rx="2" fill="white"/>
    
    <!-- Content lines -->
    <rect x="2" y="4" width="14" height="1.5" fill="#667eea"/>
    <rect x="2" y="7" width="10" height="1" fill="#999"/>
    <rect x="2" y="9" width="12" height="1" fill="#999"/>
    <rect x="2" y="11" width="8" height="1" fill="#999"/>
    <rect x="2" y="14" width="11" height="1" fill="#999"/>
    <rect x="2" y="16" width="9" height="1" fill="#999"/>
    <rect x="2" y="18" width="13" height="1" fill="#999"/>
  </g>
  
  <!-- Logo text -->
  <text x="115" y="32" font-family="Arial, sans-serif" font-size="16" font-weight="bold" 
        text-anchor="middle" fill="white">NEWS CENTER</text>
</svg>
