DEBUG - 2025-06-23 12:13:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-23 12:13:19 --> Error connecting to the database: mysqli_sql_exception: Unknown database 'sukses' in C:\xampp\htdocs\ci4\system\Database\MySQLi\Connection.php:201
Stack trace:
#0 C:\xampp\htdocs\ci4\system\Database\MySQLi\Connection.php(201): mysqli->real_connect('localhost', 'root', Object(SensitiveParameterValue), 'sukses', 3306, '', 0)
#1 C:\xampp\htdocs\ci4\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#2 C:\xampp\htdocs\ci4\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#3 C:\xampp\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 C:\xampp\htdocs\ci4\system\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#5 C:\xampp\htdocs\ci4\system\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
#6 C:\xampp\htdocs\ci4\app\Cells\ArticleLatest.php(22): CodeIgniter\BaseModel->findAll()
#7 C:\xampp\htdocs\ci4\system\View\Cell.php(307): App\Cells\ArticleLatest->render(NULL)
#8 C:\xampp\htdocs\ci4\system\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', Array, 'App\\Cells\\Artic...')
#9 C:\xampp\htdocs\ci4\system\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\Artic...', Array, 0, 'AppCellsArticle...')
#10 C:\xampp\htdocs\ci4\app\Views\template\footer.php(4): view_cell('App\\Cells\\Artic...', Array)
#11 C:\xampp\htdocs\ci4\system\View\View.php(224): include('C:\\xampp\\htdocs...')
#12 C:\xampp\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#13 C:\xampp\htdocs\ci4\system\View\View.php(475): CodeIgniter\View\View->render('template/footer', NULL, true)
#14 C:\xampp\htdocs\ci4\app\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
#15 C:\xampp\htdocs\ci4\system\View\View.php(224): include('C:\\xampp\\htdocs...')
#16 C:\xampp\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#17 C:\xampp\htdocs\ci4\system\Common.php(1173): CodeIgniter\View\View->render('home', Array, true)
#18 C:\xampp\htdocs\ci4\app\Controllers\Page.php(7): view('home', Array)
#19 C:\xampp\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#20 C:\xampp\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#21 C:\xampp\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#22 C:\xampp\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#23 C:\xampp\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#24 C:\xampp\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#25 C:\xampp\htdocs\ci4\system\rewrite.php(44): require_once('C:\\xampp\\htdocs...')
#26 {main}

Next CodeIgniter\Database\Exceptions\DatabaseException: Unknown database 'sukses' in C:\xampp\htdocs\ci4\system\Database\MySQLi\Connection.php:246
Stack trace:
#0 C:\xampp\htdocs\ci4\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#1 C:\xampp\htdocs\ci4\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#2 C:\xampp\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#3 C:\xampp\htdocs\ci4\system\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#4 C:\xampp\htdocs\ci4\system\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
#5 C:\xampp\htdocs\ci4\app\Cells\ArticleLatest.php(22): CodeIgniter\BaseModel->findAll()
#6 C:\xampp\htdocs\ci4\system\View\Cell.php(307): App\Cells\ArticleLatest->render(NULL)
#7 C:\xampp\htdocs\ci4\system\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', Array, 'App\\Cells\\Artic...')
#8 C:\xampp\htdocs\ci4\system\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\Artic...', Array, 0, 'AppCellsArticle...')
#9 C:\xampp\htdocs\ci4\app\Views\template\footer.php(4): view_cell('App\\Cells\\Artic...', Array)
#10 C:\xampp\htdocs\ci4\system\View\View.php(224): include('C:\\xampp\\htdocs...')
#11 C:\xampp\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#12 C:\xampp\htdocs\ci4\system\View\View.php(475): CodeIgniter\View\View->render('template/footer', NULL, true)
#13 C:\xampp\htdocs\ci4\app\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
#14 C:\xampp\htdocs\ci4\system\View\View.php(224): include('C:\\xampp\\htdocs...')
#15 C:\xampp\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#16 C:\xampp\htdocs\ci4\system\Common.php(1173): CodeIgniter\View\View->render('home', Array, true)
#17 C:\xampp\htdocs\ci4\app\Controllers\Page.php(7): view('home', Array)
#18 C:\xampp\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#19 C:\xampp\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#20 C:\xampp\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#21 C:\xampp\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#22 C:\xampp\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#23 C:\xampp\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#24 C:\xampp\htdocs\ci4\system\rewrite.php(44): require_once('C:\\xampp\\htdocs...')
#25 {main}
CRITICAL - 2025-06-23 12:13:19 --> CodeIgniter\Database\Exceptions\DatabaseException: Unable to connect to the database.
Main connection [MySQLi]: Unknown database 'sukses'
[Method: GET, Route: /]
in SYSTEMPATH\Database\BaseConnection.php on line 467.
 1 SYSTEMPATH\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
 2 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `artikel`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 3 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 4 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 5 APPPATH\Cells\ArticleLatest.php(22): CodeIgniter\BaseModel->findAll()
 6 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 7 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 8 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
 9 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
10 SYSTEMPATH\View\View.php(224): include('C:\\xampp\\htdocs\\ci4\\app\\Views\\template\\footer.php')
11 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
12 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
13 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
14 SYSTEMPATH\View\View.php(224): include('C:\\xampp\\htdocs\\ci4\\app\\Views\\home.php')
15 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
16 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
17 APPPATH\Controllers\Page.php(7): view('home', [...])
18 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
19 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
20 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
21 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
22 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
23 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
24 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\ci4\\public\\index.php')
DEBUG - 2025-06-23 12:14:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-23 12:14:50 --> mysqli_sql_exception: Table 'sukses.artikel' doesn't exist in engine in C:\xampp\htdocs\ci4\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 C:\xampp\htdocs\ci4\system\Database\MySQLi\Connection.php(327): mysqli->query('SELECT *\nFROM `...', 0)
#1 C:\xampp\htdocs\ci4\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *\nFROM `...')
#2 C:\xampp\htdocs\ci4\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *\nFROM `...')
#3 C:\xampp\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 C:\xampp\htdocs\ci4\system\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#5 C:\xampp\htdocs\ci4\system\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
#6 C:\xampp\htdocs\ci4\app\Cells\ArticleLatest.php(22): CodeIgniter\BaseModel->findAll()
#7 C:\xampp\htdocs\ci4\system\View\Cell.php(307): App\Cells\ArticleLatest->render(NULL)
#8 C:\xampp\htdocs\ci4\system\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', Array, 'App\\Cells\\Artic...')
#9 C:\xampp\htdocs\ci4\system\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\Artic...', Array, 0, 'AppCellsArticle...')
#10 C:\xampp\htdocs\ci4\app\Views\template\footer.php(4): view_cell('App\\Cells\\Artic...', Array)
#11 C:\xampp\htdocs\ci4\system\View\View.php(224): include('C:\\xampp\\htdocs...')
#12 C:\xampp\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#13 C:\xampp\htdocs\ci4\system\View\View.php(475): CodeIgniter\View\View->render('template/footer', NULL, true)
#14 C:\xampp\htdocs\ci4\app\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
#15 C:\xampp\htdocs\ci4\system\View\View.php(224): include('C:\\xampp\\htdocs...')
#16 C:\xampp\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#17 C:\xampp\htdocs\ci4\system\Common.php(1173): CodeIgniter\View\View->render('home', Array, true)
#18 C:\xampp\htdocs\ci4\app\Controllers\Page.php(7): view('home', Array)
#19 C:\xampp\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#20 C:\xampp\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#21 C:\xampp\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#22 C:\xampp\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#23 C:\xampp\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#24 C:\xampp\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#25 C:\xampp\htdocs\ci4\system\rewrite.php(44): require_once('C:\\xampp\\htdocs...')
#26 {main}
CRITICAL - 2025-06-23 12:14:50 --> CodeIgniter\Database\Exceptions\DatabaseException: Table 'sukses.artikel' doesn't exist in engine
[Method: GET, Route: /]
in SYSTEMPATH\Database\BaseConnection.php on line 692.
 1 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `artikel`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 2 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 3 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 4 APPPATH\Cells\ArticleLatest.php(22): CodeIgniter\BaseModel->findAll()
 5 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 6 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 7 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
 8 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
 9 SYSTEMPATH\View\View.php(224): include('C:\\xampp\\htdocs\\ci4\\app\\Views\\template\\footer.php')
10 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
11 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
12 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
13 SYSTEMPATH\View\View.php(224): include('C:\\xampp\\htdocs\\ci4\\app\\Views\\home.php')
14 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
15 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
16 APPPATH\Controllers\Page.php(7): view('home', [...])
17 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
18 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
19 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
20 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
21 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
22 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
23 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\ci4\\public\\index.php')
CRITICAL - 2025-06-23 12:14:50 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Table 'sukses.artikel' doesn't exist in engine
in SYSTEMPATH\Database\MySQLi\Connection.php on line 332.
 1 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `artikel`
ORDER BY `created_at` DESC
 LIMIT 5')
 2 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `artikel`
ORDER BY `created_at` DESC
 LIMIT 5')
 3 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `artikel`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 4 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 5 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 6 APPPATH\Cells\ArticleLatest.php(22): CodeIgniter\BaseModel->findAll()
 7 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 8 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 9 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
10 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
11 SYSTEMPATH\View\View.php(224): include('C:\\xampp\\htdocs\\ci4\\app\\Views\\template\\footer.php')
12 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
13 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
14 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
15 SYSTEMPATH\View\View.php(224): include('C:\\xampp\\htdocs\\ci4\\app\\Views\\home.php')
16 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
17 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
18 APPPATH\Controllers\Page.php(7): view('home', [...])
19 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
20 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
21 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
22 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
23 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
24 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
25 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\ci4\\public\\index.php')
CRITICAL - 2025-06-23 12:14:50 --> [Caused by] mysqli_sql_exception: Table 'sukses.artikel' doesn't exist in engine
in SYSTEMPATH\Database\MySQLi\Connection.php on line 327.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(327): mysqli->query('SELECT *
FROM `artikel`
ORDER BY `created_at` DESC
 LIMIT 5', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `artikel`
ORDER BY `created_at` DESC
 LIMIT 5')
 3 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `artikel`
ORDER BY `created_at` DESC
 LIMIT 5')
 4 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `artikel`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 5 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 6 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 7 APPPATH\Cells\ArticleLatest.php(22): CodeIgniter\BaseModel->findAll()
 8 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 9 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
10 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
11 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
12 SYSTEMPATH\View\View.php(224): include('C:\\xampp\\htdocs\\ci4\\app\\Views\\template\\footer.php')
13 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
14 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
15 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
16 SYSTEMPATH\View\View.php(224): include('C:\\xampp\\htdocs\\ci4\\app\\Views\\home.php')
17 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
18 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
19 APPPATH\Controllers\Page.php(7): view('home', [...])
20 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
21 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
22 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
23 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
24 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
25 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
26 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\ci4\\public\\index.php')
DEBUG - 2025-06-23 12:14:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-23 12:14:51 --> mysqli_sql_exception: Table 'sukses.artikel' doesn't exist in engine in C:\xampp\htdocs\ci4\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 C:\xampp\htdocs\ci4\system\Database\MySQLi\Connection.php(327): mysqli->query('SELECT *\nFROM `...', 0)
#1 C:\xampp\htdocs\ci4\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *\nFROM `...')
#2 C:\xampp\htdocs\ci4\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *\nFROM `...')
#3 C:\xampp\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 C:\xampp\htdocs\ci4\system\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#5 C:\xampp\htdocs\ci4\system\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
#6 C:\xampp\htdocs\ci4\app\Cells\ArticleLatest.php(22): CodeIgniter\BaseModel->findAll()
#7 C:\xampp\htdocs\ci4\system\View\Cell.php(307): App\Cells\ArticleLatest->render(NULL)
#8 C:\xampp\htdocs\ci4\system\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', Array, 'App\\Cells\\Artic...')
#9 C:\xampp\htdocs\ci4\system\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\Artic...', Array, 0, 'AppCellsArticle...')
#10 C:\xampp\htdocs\ci4\app\Views\template\footer.php(4): view_cell('App\\Cells\\Artic...', Array)
#11 C:\xampp\htdocs\ci4\system\View\View.php(224): include('C:\\xampp\\htdocs...')
#12 C:\xampp\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#13 C:\xampp\htdocs\ci4\system\View\View.php(475): CodeIgniter\View\View->render('template/footer', NULL, true)
#14 C:\xampp\htdocs\ci4\app\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
#15 C:\xampp\htdocs\ci4\system\View\View.php(224): include('C:\\xampp\\htdocs...')
#16 C:\xampp\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#17 C:\xampp\htdocs\ci4\system\Common.php(1173): CodeIgniter\View\View->render('home', Array, true)
#18 C:\xampp\htdocs\ci4\app\Controllers\Page.php(7): view('home', Array)
#19 C:\xampp\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#20 C:\xampp\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#21 C:\xampp\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#22 C:\xampp\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#23 C:\xampp\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#24 C:\xampp\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#25 C:\xampp\htdocs\ci4\system\rewrite.php(44): require_once('C:\\xampp\\htdocs...')
#26 {main}
CRITICAL - 2025-06-23 12:14:51 --> CodeIgniter\Database\Exceptions\DatabaseException: Table 'sukses.artikel' doesn't exist in engine
[Method: GET, Route: /]
in SYSTEMPATH\Database\BaseConnection.php on line 692.
 1 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `artikel`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 2 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 3 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 4 APPPATH\Cells\ArticleLatest.php(22): CodeIgniter\BaseModel->findAll()
 5 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 6 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 7 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
 8 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
 9 SYSTEMPATH\View\View.php(224): include('C:\\xampp\\htdocs\\ci4\\app\\Views\\template\\footer.php')
10 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
11 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
12 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
13 SYSTEMPATH\View\View.php(224): include('C:\\xampp\\htdocs\\ci4\\app\\Views\\home.php')
14 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
15 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
16 APPPATH\Controllers\Page.php(7): view('home', [...])
17 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
18 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
19 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
20 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
21 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
22 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
23 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\ci4\\public\\index.php')
CRITICAL - 2025-06-23 12:14:51 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Table 'sukses.artikel' doesn't exist in engine
in SYSTEMPATH\Database\MySQLi\Connection.php on line 332.
 1 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `artikel`
ORDER BY `created_at` DESC
 LIMIT 5')
 2 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `artikel`
ORDER BY `created_at` DESC
 LIMIT 5')
 3 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `artikel`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 4 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 5 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 6 APPPATH\Cells\ArticleLatest.php(22): CodeIgniter\BaseModel->findAll()
 7 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 8 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 9 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
10 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
11 SYSTEMPATH\View\View.php(224): include('C:\\xampp\\htdocs\\ci4\\app\\Views\\template\\footer.php')
12 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
13 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
14 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
15 SYSTEMPATH\View\View.php(224): include('C:\\xampp\\htdocs\\ci4\\app\\Views\\home.php')
16 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
17 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
18 APPPATH\Controllers\Page.php(7): view('home', [...])
19 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
20 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
21 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
22 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
23 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
24 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
25 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\ci4\\public\\index.php')
CRITICAL - 2025-06-23 12:14:51 --> [Caused by] mysqli_sql_exception: Table 'sukses.artikel' doesn't exist in engine
in SYSTEMPATH\Database\MySQLi\Connection.php on line 327.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(327): mysqli->query('SELECT *
FROM `artikel`
ORDER BY `created_at` DESC
 LIMIT 5', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `artikel`
ORDER BY `created_at` DESC
 LIMIT 5')
 3 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `artikel`
ORDER BY `created_at` DESC
 LIMIT 5')
 4 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `artikel`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 5 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 6 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 7 APPPATH\Cells\ArticleLatest.php(22): CodeIgniter\BaseModel->findAll()
 8 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 9 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
10 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
11 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
12 SYSTEMPATH\View\View.php(224): include('C:\\xampp\\htdocs\\ci4\\app\\Views\\template\\footer.php')
13 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
14 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
15 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
16 SYSTEMPATH\View\View.php(224): include('C:\\xampp\\htdocs\\ci4\\app\\Views\\home.php')
17 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
18 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
19 APPPATH\Controllers\Page.php(7): view('home', [...])
20 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
21 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
22 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
23 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
24 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
25 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
26 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\ci4\\public\\index.php')
DEBUG - 2025-06-23 12:14:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-23 12:14:51 --> mysqli_sql_exception: Table 'sukses.artikel' doesn't exist in engine in C:\xampp\htdocs\ci4\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 C:\xampp\htdocs\ci4\system\Database\MySQLi\Connection.php(327): mysqli->query('SELECT *\nFROM `...', 0)
#1 C:\xampp\htdocs\ci4\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *\nFROM `...')
#2 C:\xampp\htdocs\ci4\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *\nFROM `...')
#3 C:\xampp\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 C:\xampp\htdocs\ci4\system\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#5 C:\xampp\htdocs\ci4\system\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
#6 C:\xampp\htdocs\ci4\app\Cells\ArticleLatest.php(22): CodeIgniter\BaseModel->findAll()
#7 C:\xampp\htdocs\ci4\system\View\Cell.php(307): App\Cells\ArticleLatest->render(NULL)
#8 C:\xampp\htdocs\ci4\system\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', Array, 'App\\Cells\\Artic...')
#9 C:\xampp\htdocs\ci4\system\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\Artic...', Array, 0, 'AppCellsArticle...')
#10 C:\xampp\htdocs\ci4\app\Views\template\footer.php(4): view_cell('App\\Cells\\Artic...', Array)
#11 C:\xampp\htdocs\ci4\system\View\View.php(224): include('C:\\xampp\\htdocs...')
#12 C:\xampp\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#13 C:\xampp\htdocs\ci4\system\View\View.php(475): CodeIgniter\View\View->render('template/footer', NULL, true)
#14 C:\xampp\htdocs\ci4\app\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
#15 C:\xampp\htdocs\ci4\system\View\View.php(224): include('C:\\xampp\\htdocs...')
#16 C:\xampp\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#17 C:\xampp\htdocs\ci4\system\Common.php(1173): CodeIgniter\View\View->render('home', Array, true)
#18 C:\xampp\htdocs\ci4\app\Controllers\Page.php(7): view('home', Array)
#19 C:\xampp\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#20 C:\xampp\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#21 C:\xampp\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#22 C:\xampp\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#23 C:\xampp\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#24 C:\xampp\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#25 C:\xampp\htdocs\ci4\system\rewrite.php(44): require_once('C:\\xampp\\htdocs...')
#26 {main}
CRITICAL - 2025-06-23 12:14:51 --> CodeIgniter\Database\Exceptions\DatabaseException: Table 'sukses.artikel' doesn't exist in engine
[Method: GET, Route: /]
in SYSTEMPATH\Database\BaseConnection.php on line 692.
 1 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `artikel`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 2 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 3 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 4 APPPATH\Cells\ArticleLatest.php(22): CodeIgniter\BaseModel->findAll()
 5 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 6 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 7 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
 8 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
 9 SYSTEMPATH\View\View.php(224): include('C:\\xampp\\htdocs\\ci4\\app\\Views\\template\\footer.php')
10 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
11 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
12 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
13 SYSTEMPATH\View\View.php(224): include('C:\\xampp\\htdocs\\ci4\\app\\Views\\home.php')
14 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
15 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
16 APPPATH\Controllers\Page.php(7): view('home', [...])
17 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
18 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
19 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
20 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
21 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
22 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
23 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\ci4\\public\\index.php')
CRITICAL - 2025-06-23 12:14:51 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Table 'sukses.artikel' doesn't exist in engine
in SYSTEMPATH\Database\MySQLi\Connection.php on line 332.
 1 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `artikel`
ORDER BY `created_at` DESC
 LIMIT 5')
 2 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `artikel`
ORDER BY `created_at` DESC
 LIMIT 5')
 3 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `artikel`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 4 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 5 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 6 APPPATH\Cells\ArticleLatest.php(22): CodeIgniter\BaseModel->findAll()
 7 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 8 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 9 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
10 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
11 SYSTEMPATH\View\View.php(224): include('C:\\xampp\\htdocs\\ci4\\app\\Views\\template\\footer.php')
12 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
13 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
14 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
15 SYSTEMPATH\View\View.php(224): include('C:\\xampp\\htdocs\\ci4\\app\\Views\\home.php')
16 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
17 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
18 APPPATH\Controllers\Page.php(7): view('home', [...])
19 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
20 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
21 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
22 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
23 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
24 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
25 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\ci4\\public\\index.php')
CRITICAL - 2025-06-23 12:14:51 --> [Caused by] mysqli_sql_exception: Table 'sukses.artikel' doesn't exist in engine
in SYSTEMPATH\Database\MySQLi\Connection.php on line 327.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(327): mysqli->query('SELECT *
FROM `artikel`
ORDER BY `created_at` DESC
 LIMIT 5', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `artikel`
ORDER BY `created_at` DESC
 LIMIT 5')
 3 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `artikel`
ORDER BY `created_at` DESC
 LIMIT 5')
 4 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `artikel`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 5 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 6 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 7 APPPATH\Cells\ArticleLatest.php(22): CodeIgniter\BaseModel->findAll()
 8 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 9 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
10 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
11 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
12 SYSTEMPATH\View\View.php(224): include('C:\\xampp\\htdocs\\ci4\\app\\Views\\template\\footer.php')
13 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
14 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
15 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
16 SYSTEMPATH\View\View.php(224): include('C:\\xampp\\htdocs\\ci4\\app\\Views\\home.php')
17 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
18 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
19 APPPATH\Controllers\Page.php(7): view('home', [...])
20 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
21 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
22 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
23 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
24 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
25 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
26 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\ci4\\public\\index.php')
DEBUG - 2025-06-23 12:14:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-23 12:14:52 --> mysqli_sql_exception: Table 'sukses.artikel' doesn't exist in engine in C:\xampp\htdocs\ci4\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 C:\xampp\htdocs\ci4\system\Database\MySQLi\Connection.php(327): mysqli->query('SELECT *\nFROM `...', 0)
#1 C:\xampp\htdocs\ci4\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *\nFROM `...')
#2 C:\xampp\htdocs\ci4\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *\nFROM `...')
#3 C:\xampp\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 C:\xampp\htdocs\ci4\system\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#5 C:\xampp\htdocs\ci4\system\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
#6 C:\xampp\htdocs\ci4\app\Cells\ArticleLatest.php(22): CodeIgniter\BaseModel->findAll()
#7 C:\xampp\htdocs\ci4\system\View\Cell.php(307): App\Cells\ArticleLatest->render(NULL)
#8 C:\xampp\htdocs\ci4\system\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', Array, 'App\\Cells\\Artic...')
#9 C:\xampp\htdocs\ci4\system\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\Artic...', Array, 0, 'AppCellsArticle...')
#10 C:\xampp\htdocs\ci4\app\Views\template\footer.php(4): view_cell('App\\Cells\\Artic...', Array)
#11 C:\xampp\htdocs\ci4\system\View\View.php(224): include('C:\\xampp\\htdocs...')
#12 C:\xampp\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#13 C:\xampp\htdocs\ci4\system\View\View.php(475): CodeIgniter\View\View->render('template/footer', NULL, true)
#14 C:\xampp\htdocs\ci4\app\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
#15 C:\xampp\htdocs\ci4\system\View\View.php(224): include('C:\\xampp\\htdocs...')
#16 C:\xampp\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#17 C:\xampp\htdocs\ci4\system\Common.php(1173): CodeIgniter\View\View->render('home', Array, true)
#18 C:\xampp\htdocs\ci4\app\Controllers\Page.php(7): view('home', Array)
#19 C:\xampp\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#20 C:\xampp\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#21 C:\xampp\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#22 C:\xampp\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#23 C:\xampp\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#24 C:\xampp\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#25 C:\xampp\htdocs\ci4\system\rewrite.php(44): require_once('C:\\xampp\\htdocs...')
#26 {main}
CRITICAL - 2025-06-23 12:14:52 --> CodeIgniter\Database\Exceptions\DatabaseException: Table 'sukses.artikel' doesn't exist in engine
[Method: GET, Route: /]
in SYSTEMPATH\Database\BaseConnection.php on line 692.
 1 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `artikel`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 2 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 3 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 4 APPPATH\Cells\ArticleLatest.php(22): CodeIgniter\BaseModel->findAll()
 5 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 6 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 7 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
 8 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
 9 SYSTEMPATH\View\View.php(224): include('C:\\xampp\\htdocs\\ci4\\app\\Views\\template\\footer.php')
10 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
11 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
12 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
13 SYSTEMPATH\View\View.php(224): include('C:\\xampp\\htdocs\\ci4\\app\\Views\\home.php')
14 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
15 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
16 APPPATH\Controllers\Page.php(7): view('home', [...])
17 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
18 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
19 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
20 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
21 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
22 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
23 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\ci4\\public\\index.php')
CRITICAL - 2025-06-23 12:14:52 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Table 'sukses.artikel' doesn't exist in engine
in SYSTEMPATH\Database\MySQLi\Connection.php on line 332.
 1 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `artikel`
ORDER BY `created_at` DESC
 LIMIT 5')
 2 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `artikel`
ORDER BY `created_at` DESC
 LIMIT 5')
 3 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `artikel`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 4 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 5 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 6 APPPATH\Cells\ArticleLatest.php(22): CodeIgniter\BaseModel->findAll()
 7 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 8 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 9 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
10 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
11 SYSTEMPATH\View\View.php(224): include('C:\\xampp\\htdocs\\ci4\\app\\Views\\template\\footer.php')
12 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
13 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
14 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
15 SYSTEMPATH\View\View.php(224): include('C:\\xampp\\htdocs\\ci4\\app\\Views\\home.php')
16 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
17 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
18 APPPATH\Controllers\Page.php(7): view('home', [...])
19 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
20 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
21 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
22 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
23 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
24 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
25 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\ci4\\public\\index.php')
CRITICAL - 2025-06-23 12:14:52 --> [Caused by] mysqli_sql_exception: Table 'sukses.artikel' doesn't exist in engine
in SYSTEMPATH\Database\MySQLi\Connection.php on line 327.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(327): mysqli->query('SELECT *
FROM `artikel`
ORDER BY `created_at` DESC
 LIMIT 5', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `artikel`
ORDER BY `created_at` DESC
 LIMIT 5')
 3 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `artikel`
ORDER BY `created_at` DESC
 LIMIT 5')
 4 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `artikel`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 5 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 6 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 7 APPPATH\Cells\ArticleLatest.php(22): CodeIgniter\BaseModel->findAll()
 8 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 9 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
10 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
11 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
12 SYSTEMPATH\View\View.php(224): include('C:\\xampp\\htdocs\\ci4\\app\\Views\\template\\footer.php')
13 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
14 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
15 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
16 SYSTEMPATH\View\View.php(224): include('C:\\xampp\\htdocs\\ci4\\app\\Views\\home.php')
17 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
18 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
19 APPPATH\Controllers\Page.php(7): view('home', [...])
20 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
21 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
22 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
23 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
24 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
25 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
26 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\ci4\\public\\index.php')
DEBUG - 2025-06-23 12:14:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-23 12:14:53 --> mysqli_sql_exception: Table 'sukses.artikel' doesn't exist in engine in C:\xampp\htdocs\ci4\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 C:\xampp\htdocs\ci4\system\Database\MySQLi\Connection.php(327): mysqli->query('SELECT *\nFROM `...', 0)
#1 C:\xampp\htdocs\ci4\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *\nFROM `...')
#2 C:\xampp\htdocs\ci4\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *\nFROM `...')
#3 C:\xampp\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 C:\xampp\htdocs\ci4\system\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#5 C:\xampp\htdocs\ci4\system\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
#6 C:\xampp\htdocs\ci4\app\Cells\ArticleLatest.php(22): CodeIgniter\BaseModel->findAll()
#7 C:\xampp\htdocs\ci4\system\View\Cell.php(307): App\Cells\ArticleLatest->render(NULL)
#8 C:\xampp\htdocs\ci4\system\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', Array, 'App\\Cells\\Artic...')
#9 C:\xampp\htdocs\ci4\system\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\Artic...', Array, 0, 'AppCellsArticle...')
#10 C:\xampp\htdocs\ci4\app\Views\template\footer.php(4): view_cell('App\\Cells\\Artic...', Array)
#11 C:\xampp\htdocs\ci4\system\View\View.php(224): include('C:\\xampp\\htdocs...')
#12 C:\xampp\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#13 C:\xampp\htdocs\ci4\system\View\View.php(475): CodeIgniter\View\View->render('template/footer', NULL, true)
#14 C:\xampp\htdocs\ci4\app\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
#15 C:\xampp\htdocs\ci4\system\View\View.php(224): include('C:\\xampp\\htdocs...')
#16 C:\xampp\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#17 C:\xampp\htdocs\ci4\system\Common.php(1173): CodeIgniter\View\View->render('home', Array, true)
#18 C:\xampp\htdocs\ci4\app\Controllers\Page.php(7): view('home', Array)
#19 C:\xampp\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#20 C:\xampp\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#21 C:\xampp\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#22 C:\xampp\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#23 C:\xampp\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#24 C:\xampp\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#25 C:\xampp\htdocs\ci4\system\rewrite.php(44): require_once('C:\\xampp\\htdocs...')
#26 {main}
CRITICAL - 2025-06-23 12:14:53 --> CodeIgniter\Database\Exceptions\DatabaseException: Table 'sukses.artikel' doesn't exist in engine
[Method: GET, Route: /]
in SYSTEMPATH\Database\BaseConnection.php on line 692.
 1 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `artikel`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 2 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 3 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 4 APPPATH\Cells\ArticleLatest.php(22): CodeIgniter\BaseModel->findAll()
 5 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 6 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 7 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
 8 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
 9 SYSTEMPATH\View\View.php(224): include('C:\\xampp\\htdocs\\ci4\\app\\Views\\template\\footer.php')
10 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
11 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
12 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
13 SYSTEMPATH\View\View.php(224): include('C:\\xampp\\htdocs\\ci4\\app\\Views\\home.php')
14 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
15 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
16 APPPATH\Controllers\Page.php(7): view('home', [...])
17 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
18 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
19 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
20 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
21 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
22 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
23 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\ci4\\public\\index.php')
CRITICAL - 2025-06-23 12:14:53 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Table 'sukses.artikel' doesn't exist in engine
in SYSTEMPATH\Database\MySQLi\Connection.php on line 332.
 1 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `artikel`
ORDER BY `created_at` DESC
 LIMIT 5')
 2 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `artikel`
ORDER BY `created_at` DESC
 LIMIT 5')
 3 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `artikel`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 4 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 5 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 6 APPPATH\Cells\ArticleLatest.php(22): CodeIgniter\BaseModel->findAll()
 7 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 8 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 9 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
10 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
11 SYSTEMPATH\View\View.php(224): include('C:\\xampp\\htdocs\\ci4\\app\\Views\\template\\footer.php')
12 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
13 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
14 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
15 SYSTEMPATH\View\View.php(224): include('C:\\xampp\\htdocs\\ci4\\app\\Views\\home.php')
16 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
17 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
18 APPPATH\Controllers\Page.php(7): view('home', [...])
19 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
20 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
21 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
22 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
23 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
24 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
25 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\ci4\\public\\index.php')
CRITICAL - 2025-06-23 12:14:53 --> [Caused by] mysqli_sql_exception: Table 'sukses.artikel' doesn't exist in engine
in SYSTEMPATH\Database\MySQLi\Connection.php on line 327.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(327): mysqli->query('SELECT *
FROM `artikel`
ORDER BY `created_at` DESC
 LIMIT 5', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `artikel`
ORDER BY `created_at` DESC
 LIMIT 5')
 3 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `artikel`
ORDER BY `created_at` DESC
 LIMIT 5')
 4 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `artikel`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 5 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 6 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 7 APPPATH\Cells\ArticleLatest.php(22): CodeIgniter\BaseModel->findAll()
 8 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 9 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
10 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
11 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
12 SYSTEMPATH\View\View.php(224): include('C:\\xampp\\htdocs\\ci4\\app\\Views\\template\\footer.php')
13 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
14 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
15 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
16 SYSTEMPATH\View\View.php(224): include('C:\\xampp\\htdocs\\ci4\\app\\Views\\home.php')
17 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
18 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
19 APPPATH\Controllers\Page.php(7): view('home', [...])
20 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
21 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
22 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
23 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
24 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
25 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
26 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\ci4\\public\\index.php')
DEBUG - 2025-06-23 12:14:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-23 12:14:54 --> mysqli_sql_exception: Table 'sukses.artikel' doesn't exist in engine in C:\xampp\htdocs\ci4\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 C:\xampp\htdocs\ci4\system\Database\MySQLi\Connection.php(327): mysqli->query('SELECT *\nFROM `...', 0)
#1 C:\xampp\htdocs\ci4\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *\nFROM `...')
#2 C:\xampp\htdocs\ci4\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *\nFROM `...')
#3 C:\xampp\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 C:\xampp\htdocs\ci4\system\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#5 C:\xampp\htdocs\ci4\system\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
#6 C:\xampp\htdocs\ci4\app\Cells\ArticleLatest.php(22): CodeIgniter\BaseModel->findAll()
#7 C:\xampp\htdocs\ci4\system\View\Cell.php(307): App\Cells\ArticleLatest->render(NULL)
#8 C:\xampp\htdocs\ci4\system\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', Array, 'App\\Cells\\Artic...')
#9 C:\xampp\htdocs\ci4\system\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\Artic...', Array, 0, 'AppCellsArticle...')
#10 C:\xampp\htdocs\ci4\app\Views\template\footer.php(4): view_cell('App\\Cells\\Artic...', Array)
#11 C:\xampp\htdocs\ci4\system\View\View.php(224): include('C:\\xampp\\htdocs...')
#12 C:\xampp\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#13 C:\xampp\htdocs\ci4\system\View\View.php(475): CodeIgniter\View\View->render('template/footer', NULL, true)
#14 C:\xampp\htdocs\ci4\app\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
#15 C:\xampp\htdocs\ci4\system\View\View.php(224): include('C:\\xampp\\htdocs...')
#16 C:\xampp\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#17 C:\xampp\htdocs\ci4\system\Common.php(1173): CodeIgniter\View\View->render('home', Array, true)
#18 C:\xampp\htdocs\ci4\app\Controllers\Page.php(7): view('home', Array)
#19 C:\xampp\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#20 C:\xampp\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#21 C:\xampp\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#22 C:\xampp\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#23 C:\xampp\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#24 C:\xampp\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#25 C:\xampp\htdocs\ci4\system\rewrite.php(44): require_once('C:\\xampp\\htdocs...')
#26 {main}
CRITICAL - 2025-06-23 12:14:54 --> CodeIgniter\Database\Exceptions\DatabaseException: Table 'sukses.artikel' doesn't exist in engine
[Method: GET, Route: /]
in SYSTEMPATH\Database\BaseConnection.php on line 692.
 1 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `artikel`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 2 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 3 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 4 APPPATH\Cells\ArticleLatest.php(22): CodeIgniter\BaseModel->findAll()
 5 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 6 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 7 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
 8 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
 9 SYSTEMPATH\View\View.php(224): include('C:\\xampp\\htdocs\\ci4\\app\\Views\\template\\footer.php')
10 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
11 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
12 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
13 SYSTEMPATH\View\View.php(224): include('C:\\xampp\\htdocs\\ci4\\app\\Views\\home.php')
14 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
15 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
16 APPPATH\Controllers\Page.php(7): view('home', [...])
17 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
18 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
19 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
20 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
21 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
22 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
23 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\ci4\\public\\index.php')
CRITICAL - 2025-06-23 12:14:54 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Table 'sukses.artikel' doesn't exist in engine
in SYSTEMPATH\Database\MySQLi\Connection.php on line 332.
 1 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `artikel`
ORDER BY `created_at` DESC
 LIMIT 5')
 2 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `artikel`
ORDER BY `created_at` DESC
 LIMIT 5')
 3 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `artikel`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 4 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 5 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 6 APPPATH\Cells\ArticleLatest.php(22): CodeIgniter\BaseModel->findAll()
 7 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 8 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 9 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
10 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
11 SYSTEMPATH\View\View.php(224): include('C:\\xampp\\htdocs\\ci4\\app\\Views\\template\\footer.php')
12 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
13 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
14 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
15 SYSTEMPATH\View\View.php(224): include('C:\\xampp\\htdocs\\ci4\\app\\Views\\home.php')
16 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
17 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
18 APPPATH\Controllers\Page.php(7): view('home', [...])
19 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
20 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
21 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
22 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
23 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
24 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
25 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\ci4\\public\\index.php')
CRITICAL - 2025-06-23 12:14:54 --> [Caused by] mysqli_sql_exception: Table 'sukses.artikel' doesn't exist in engine
in SYSTEMPATH\Database\MySQLi\Connection.php on line 327.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(327): mysqli->query('SELECT *
FROM `artikel`
ORDER BY `created_at` DESC
 LIMIT 5', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `artikel`
ORDER BY `created_at` DESC
 LIMIT 5')
 3 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `artikel`
ORDER BY `created_at` DESC
 LIMIT 5')
 4 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `artikel`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 5 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 6 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 7 APPPATH\Cells\ArticleLatest.php(22): CodeIgniter\BaseModel->findAll()
 8 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 9 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
10 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
11 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
12 SYSTEMPATH\View\View.php(224): include('C:\\xampp\\htdocs\\ci4\\app\\Views\\template\\footer.php')
13 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
14 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
15 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
16 SYSTEMPATH\View\View.php(224): include('C:\\xampp\\htdocs\\ci4\\app\\Views\\home.php')
17 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
18 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
19 APPPATH\Controllers\Page.php(7): view('home', [...])
20 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
21 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
22 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
23 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
24 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
25 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
26 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\ci4\\public\\index.php')
DEBUG - 2025-06-23 14:01:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-23 14:01:08 --> CodeIgniter\View\Exceptions\ViewException: Invalid file: "components/artikel_latest.php"
[Method: GET, Route: /]
in SYSTEMPATH\Exceptions\FrameworkException.php on line 39.
 1 SYSTEMPATH\View\View.php(212): CodeIgniter\Exceptions\FrameworkException::forInvalidFile('components/artikel_latest.php')
 2 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('components/artikel_latest', [], true)
 3 APPPATH\Cells\ArtikelLatest.php(25): view('components/artikel_latest', [...])
 4 SYSTEMPATH\View\Cell.php(307): App\Cells\ArtikelLatest->render(null)
 5 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArtikelLatest), 'render', [...], 'App\\Cells\\ArtikelLatest')
 6 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArtikelLatest::render', [...], 0, 'AppCellsArtikelLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
 7 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArtikelLatest::render', [...])
 8 SYSTEMPATH\View\View.php(224): include('C:\\xampp\\htdocs\\ci4\\app\\Views\\template\\footer.php')
 9 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
10 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
11 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
12 SYSTEMPATH\View\View.php(224): include('C:\\xampp\\htdocs\\ci4\\app\\Views\\home.php')
13 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
14 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
15 APPPATH\Controllers\Page.php(7): view('home', [...])
16 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
17 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
18 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
19 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
20 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
21 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
22 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\ci4\\public\\index.php')
DEBUG - 2025-06-23 14:04:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-23 14:04:16 --> ErrorException: Declaration of App\Cells\ArtikelLatest::render() must be compatible with CodeIgniter\View\Cells\Cell::render(): string
[Method: GET, Route: /]
in APPPATH\Cells\ArtikelLatest.php on line 13.
 1 [internal function]: CodeIgniter\Debug\Exceptions->shutdownHandler()
DEBUG - 2025-06-23 14:06:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-23 14:06:48 --> ErrorException: Declaration of App\Cells\ArtikelLatest::render() must be compatible with CodeIgniter\View\Cells\Cell::render(): string
[Method: GET, Route: /]
in APPPATH\Cells\ArtikelLatest.php on line 13.
 1 [internal function]: CodeIgniter\Debug\Exceptions->shutdownHandler()
DEBUG - 2025-06-23 14:06:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-23 14:06:50 --> ErrorException: Declaration of App\Cells\ArtikelLatest::render() must be compatible with CodeIgniter\View\Cells\Cell::render(): string
[Method: GET, Route: /]
in APPPATH\Cells\ArtikelLatest.php on line 13.
 1 [internal function]: CodeIgniter\Debug\Exceptions->shutdownHandler()
DEBUG - 2025-06-23 14:08:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-23 14:08:14 --> CodeIgniter\View\Exceptions\ViewException: Invalid file: "components/artikel_latest.php"
[Method: GET, Route: /]
in SYSTEMPATH\Exceptions\FrameworkException.php on line 39.
 1 SYSTEMPATH\View\View.php(212): CodeIgniter\Exceptions\FrameworkException::forInvalidFile('components/artikel_latest.php')
 2 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('components/artikel_latest', [], true)
 3 APPPATH\Cells\ArtikelLatest.php(27): view('components/artikel_latest', [...])
 4 SYSTEMPATH\View\Cell.php(233): App\Cells\ArtikelLatest->render()
 5 SYSTEMPATH\View\Cell.php(103): CodeIgniter\View\Cell->renderCell(Object(App\Cells\ArtikelLatest), 'render', [...])
 6 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArtikelLatest::render', [...], 0, 'AppCellsArtikelLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
 7 APPPATH\Views\template\footer.php(6): view_cell('App\\Cells\\ArtikelLatest::render', [...])
 8 SYSTEMPATH\View\View.php(224): include('C:\\xampp\\htdocs\\ci4\\app\\Views\\template\\footer.php')
 9 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
10 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
11 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
12 SYSTEMPATH\View\View.php(224): include('C:\\xampp\\htdocs\\ci4\\app\\Views\\home.php')
13 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
14 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
15 APPPATH\Controllers\Page.php(7): view('home', [...])
16 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
17 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
18 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
19 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
20 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
21 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
22 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\ci4\\public\\index.php')
DEBUG - 2025-06-23 14:10:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-23 14:10:36 --> CodeIgniter\View\Exceptions\ViewException: Invalid file: "components/artikel_latest.php"
[Method: GET, Route: /]
in SYSTEMPATH\Exceptions\FrameworkException.php on line 39.
 1 SYSTEMPATH\View\View.php(212): CodeIgniter\Exceptions\FrameworkException::forInvalidFile('components/artikel_latest.php')
 2 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('components/artikel_latest', [], true)
 3 APPPATH\Cells\ArtikelLatest.php(27): view('components/artikel_latest', [...])
 4 SYSTEMPATH\View\Cell.php(233): App\Cells\ArtikelLatest->render()
 5 SYSTEMPATH\View\Cell.php(103): CodeIgniter\View\Cell->renderCell(Object(App\Cells\ArtikelLatest), 'render', [...])
 6 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArtikelLatest::render', [...], 0, 'AppCellsArtikelLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
 7 APPPATH\Views\template\footer.php(6): view_cell('App\\Cells\\ArtikelLatest::render', [...])
 8 SYSTEMPATH\View\View.php(224): include('C:\\xampp\\htdocs\\ci4\\app\\Views\\template\\footer.php')
 9 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
10 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
11 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
12 SYSTEMPATH\View\View.php(224): include('C:\\xampp\\htdocs\\ci4\\app\\Views\\home.php')
13 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
14 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
15 APPPATH\Controllers\Page.php(7): view('home', [...])
16 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
17 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
18 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
19 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
20 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
21 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
22 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\ci4\\public\\index.php')
DEBUG - 2025-06-23 14:12:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 14:12:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 14:12:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 14:14:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 14:14:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 14:18:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 14:18:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 14:18:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 14:27:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 14:34:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 14:39:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 14:58:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 14:58:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 14:58:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 15:03:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 15:03:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 15:07:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 15:07:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 15:11:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 15:11:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 15:17:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 15:17:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 15:23:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-23 15:23:03 --> mysqli_sql_exception: Table 'sukses.user' doesn't exist in engine in C:\xampp\htdocs\ci4\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 C:\xampp\htdocs\ci4\system\Database\MySQLi\Connection.php(327): mysqli->query('SELECT *\nFROM `...', 0)
#1 C:\xampp\htdocs\ci4\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *\nFROM `...')
#2 C:\xampp\htdocs\ci4\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *\nFROM `...')
#3 C:\xampp\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 C:\xampp\htdocs\ci4\system\Model.php(330): CodeIgniter\Database\BaseBuilder->get()
#5 C:\xampp\htdocs\ci4\system\BaseModel.php(713): CodeIgniter\Model->doFirst()
#6 C:\xampp\htdocs\ci4\app\Controllers\User.php(32): CodeIgniter\BaseModel->first()
#7 C:\xampp\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\User->login()
#8 C:\xampp\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User))
#9 C:\xampp\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#10 C:\xampp\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#11 C:\xampp\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#12 C:\xampp\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#13 C:\xampp\htdocs\ci4\system\rewrite.php(44): require_once('C:\\xampp\\htdocs...')
#14 {main}
CRITICAL - 2025-06-23 15:23:03 --> CodeIgniter\Database\Exceptions\DatabaseException: Table 'sukses.user' doesn't exist in engine
[Method: POST, Route: user/login]
in SYSTEMPATH\Database\BaseConnection.php on line 692.
 1 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `user`
WHERE `useremail` = :useremail:
 LIMIT 1', [...], false)
 2 SYSTEMPATH\Model.php(330): CodeIgniter\Database\BaseBuilder->get()
 3 SYSTEMPATH\BaseModel.php(713): CodeIgniter\Model->doFirst()
 4 APPPATH\Controllers\User.php(32): CodeIgniter\BaseModel->first()
 5 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\User->login()
 6 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User))
 7 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
10 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
11 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\ci4\\public\\index.php')
CRITICAL - 2025-06-23 15:23:03 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Table 'sukses.user' doesn't exist in engine
in SYSTEMPATH\Database\MySQLi\Connection.php on line 332.
 1 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `user`
WHERE `useremail` = \'<EMAIL>\'
 LIMIT 1')
 2 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `user`
WHERE `useremail` = \'<EMAIL>\'
 LIMIT 1')
 3 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `user`
WHERE `useremail` = :useremail:
 LIMIT 1', [...], false)
 4 SYSTEMPATH\Model.php(330): CodeIgniter\Database\BaseBuilder->get()
 5 SYSTEMPATH\BaseModel.php(713): CodeIgniter\Model->doFirst()
 6 APPPATH\Controllers\User.php(32): CodeIgniter\BaseModel->first()
 7 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\User->login()
 8 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User))
 9 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
10 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
11 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
12 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
13 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\ci4\\public\\index.php')
CRITICAL - 2025-06-23 15:23:03 --> [Caused by] mysqli_sql_exception: Table 'sukses.user' doesn't exist in engine
in SYSTEMPATH\Database\MySQLi\Connection.php on line 327.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(327): mysqli->query('SELECT *
FROM `user`
WHERE `useremail` = \'<EMAIL>\'
 LIMIT 1', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `user`
WHERE `useremail` = \'<EMAIL>\'
 LIMIT 1')
 3 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `user`
WHERE `useremail` = \'<EMAIL>\'
 LIMIT 1')
 4 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `user`
WHERE `useremail` = :useremail:
 LIMIT 1', [...], false)
 5 SYSTEMPATH\Model.php(330): CodeIgniter\Database\BaseBuilder->get()
 6 SYSTEMPATH\BaseModel.php(713): CodeIgniter\Model->doFirst()
 7 APPPATH\Controllers\User.php(32): CodeIgniter\BaseModel->first()
 8 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\User->login()
 9 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User))
10 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
13 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
14 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\ci4\\public\\index.php')
DEBUG - 2025-06-23 15:26:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-23 15:26:27 --> mysqli_sql_exception: Table 'sukses.user' doesn't exist in engine in C:\xampp\htdocs\ci4\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 C:\xampp\htdocs\ci4\system\Database\MySQLi\Connection.php(327): mysqli->query('SELECT *\nFROM `...', 0)
#1 C:\xampp\htdocs\ci4\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *\nFROM `...')
#2 C:\xampp\htdocs\ci4\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *\nFROM `...')
#3 C:\xampp\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 C:\xampp\htdocs\ci4\system\Model.php(330): CodeIgniter\Database\BaseBuilder->get()
#5 C:\xampp\htdocs\ci4\system\BaseModel.php(713): CodeIgniter\Model->doFirst()
#6 C:\xampp\htdocs\ci4\app\Controllers\User.php(32): CodeIgniter\BaseModel->first()
#7 C:\xampp\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\User->login()
#8 C:\xampp\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User))
#9 C:\xampp\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#10 C:\xampp\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#11 C:\xampp\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#12 C:\xampp\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#13 C:\xampp\htdocs\ci4\system\rewrite.php(44): require_once('C:\\xampp\\htdocs...')
#14 {main}
CRITICAL - 2025-06-23 15:26:27 --> CodeIgniter\Database\Exceptions\DatabaseException: Table 'sukses.user' doesn't exist in engine
[Method: POST, Route: user/login]
in SYSTEMPATH\Database\BaseConnection.php on line 692.
 1 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `user`
WHERE `useremail` = :useremail:
 LIMIT 1', [...], false)
 2 SYSTEMPATH\Model.php(330): CodeIgniter\Database\BaseBuilder->get()
 3 SYSTEMPATH\BaseModel.php(713): CodeIgniter\Model->doFirst()
 4 APPPATH\Controllers\User.php(32): CodeIgniter\BaseModel->first()
 5 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\User->login()
 6 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User))
 7 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
10 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
11 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\ci4\\public\\index.php')
CRITICAL - 2025-06-23 15:26:27 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Table 'sukses.user' doesn't exist in engine
in SYSTEMPATH\Database\MySQLi\Connection.php on line 332.
 1 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `user`
WHERE `useremail` = \'<EMAIL>\'
 LIMIT 1')
 2 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `user`
WHERE `useremail` = \'<EMAIL>\'
 LIMIT 1')
 3 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `user`
WHERE `useremail` = :useremail:
 LIMIT 1', [...], false)
 4 SYSTEMPATH\Model.php(330): CodeIgniter\Database\BaseBuilder->get()
 5 SYSTEMPATH\BaseModel.php(713): CodeIgniter\Model->doFirst()
 6 APPPATH\Controllers\User.php(32): CodeIgniter\BaseModel->first()
 7 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\User->login()
 8 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User))
 9 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
10 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
11 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
12 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
13 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\ci4\\public\\index.php')
CRITICAL - 2025-06-23 15:26:27 --> [Caused by] mysqli_sql_exception: Table 'sukses.user' doesn't exist in engine
in SYSTEMPATH\Database\MySQLi\Connection.php on line 327.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(327): mysqli->query('SELECT *
FROM `user`
WHERE `useremail` = \'<EMAIL>\'
 LIMIT 1', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `user`
WHERE `useremail` = \'<EMAIL>\'
 LIMIT 1')
 3 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `user`
WHERE `useremail` = \'<EMAIL>\'
 LIMIT 1')
 4 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `user`
WHERE `useremail` = :useremail:
 LIMIT 1', [...], false)
 5 SYSTEMPATH\Model.php(330): CodeIgniter\Database\BaseBuilder->get()
 6 SYSTEMPATH\BaseModel.php(713): CodeIgniter\Model->doFirst()
 7 APPPATH\Controllers\User.php(32): CodeIgniter\BaseModel->first()
 8 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\User->login()
 9 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User))
10 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
13 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
14 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\ci4\\public\\index.php')
DEBUG - 2025-06-23 15:26:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-23 15:26:31 --> mysqli_sql_exception: Table 'sukses.user' doesn't exist in engine in C:\xampp\htdocs\ci4\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 C:\xampp\htdocs\ci4\system\Database\MySQLi\Connection.php(327): mysqli->query('SELECT *\nFROM `...', 0)
#1 C:\xampp\htdocs\ci4\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *\nFROM `...')
#2 C:\xampp\htdocs\ci4\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *\nFROM `...')
#3 C:\xampp\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 C:\xampp\htdocs\ci4\system\Model.php(330): CodeIgniter\Database\BaseBuilder->get()
#5 C:\xampp\htdocs\ci4\system\BaseModel.php(713): CodeIgniter\Model->doFirst()
#6 C:\xampp\htdocs\ci4\app\Controllers\User.php(32): CodeIgniter\BaseModel->first()
#7 C:\xampp\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\User->login()
#8 C:\xampp\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User))
#9 C:\xampp\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#10 C:\xampp\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#11 C:\xampp\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#12 C:\xampp\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#13 C:\xampp\htdocs\ci4\system\rewrite.php(44): require_once('C:\\xampp\\htdocs...')
#14 {main}
CRITICAL - 2025-06-23 15:26:31 --> CodeIgniter\Database\Exceptions\DatabaseException: Table 'sukses.user' doesn't exist in engine
[Method: POST, Route: user/login]
in SYSTEMPATH\Database\BaseConnection.php on line 692.
 1 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `user`
WHERE `useremail` = :useremail:
 LIMIT 1', [...], false)
 2 SYSTEMPATH\Model.php(330): CodeIgniter\Database\BaseBuilder->get()
 3 SYSTEMPATH\BaseModel.php(713): CodeIgniter\Model->doFirst()
 4 APPPATH\Controllers\User.php(32): CodeIgniter\BaseModel->first()
 5 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\User->login()
 6 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User))
 7 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
10 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
11 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\ci4\\public\\index.php')
CRITICAL - 2025-06-23 15:26:31 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Table 'sukses.user' doesn't exist in engine
in SYSTEMPATH\Database\MySQLi\Connection.php on line 332.
 1 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `user`
WHERE `useremail` = \'<EMAIL>\'
 LIMIT 1')
 2 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `user`
WHERE `useremail` = \'<EMAIL>\'
 LIMIT 1')
 3 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `user`
WHERE `useremail` = :useremail:
 LIMIT 1', [...], false)
 4 SYSTEMPATH\Model.php(330): CodeIgniter\Database\BaseBuilder->get()
 5 SYSTEMPATH\BaseModel.php(713): CodeIgniter\Model->doFirst()
 6 APPPATH\Controllers\User.php(32): CodeIgniter\BaseModel->first()
 7 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\User->login()
 8 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User))
 9 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
10 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
11 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
12 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
13 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\ci4\\public\\index.php')
CRITICAL - 2025-06-23 15:26:31 --> [Caused by] mysqli_sql_exception: Table 'sukses.user' doesn't exist in engine
in SYSTEMPATH\Database\MySQLi\Connection.php on line 327.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(327): mysqli->query('SELECT *
FROM `user`
WHERE `useremail` = \'<EMAIL>\'
 LIMIT 1', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `user`
WHERE `useremail` = \'<EMAIL>\'
 LIMIT 1')
 3 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `user`
WHERE `useremail` = \'<EMAIL>\'
 LIMIT 1')
 4 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `user`
WHERE `useremail` = :useremail:
 LIMIT 1', [...], false)
 5 SYSTEMPATH\Model.php(330): CodeIgniter\Database\BaseBuilder->get()
 6 SYSTEMPATH\BaseModel.php(713): CodeIgniter\Model->doFirst()
 7 APPPATH\Controllers\User.php(32): CodeIgniter\BaseModel->first()
 8 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\User->login()
 9 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User))
10 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
13 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
14 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\ci4\\public\\index.php')
DEBUG - 2025-06-23 15:28:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 15:28:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 15:28:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 15:28:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 15:29:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 15:29:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 15:32:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 15:32:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 15:34:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 15:34:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 15:34:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 15:34:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 15:34:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 15:35:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 15:35:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 15:39:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 15:39:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 15:44:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 15:44:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 15:51:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 15:51:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 16:05:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 16:05:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 16:06:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 16:06:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 16:06:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 16:06:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 18:08:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 18:08:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 18:08:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 18:08:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 23:21:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 23:21:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 23:21:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 23:27:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 23:27:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 23:48:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 23:48:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 23:48:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 23:48:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 23:48:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 23:52:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 23:52:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 23:52:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 23:52:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
