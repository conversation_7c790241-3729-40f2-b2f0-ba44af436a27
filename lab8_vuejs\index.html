<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Article Management - Vue.js Frontend</title>

  <!-- Vue Development -->
  <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>

  <!-- Axios untuk komunikasi API -->
  <script src="https://unpkg.com/axios/dist/axios.min.js"></script>

  <!-- Bootstrap 5 CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

  <!-- Font Awesome -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

  <!-- Custom Style -->
  <link rel="stylesheet" href="assets/css/style.css" />

  <!-- Hindari error favicon -->
  <link rel="icon" href="data:,">
</head>
<body>
  <div id="app">
    <!-- Simple Header -->
    <div class="container-fluid bg-primary text-white py-4 mb-4">
      <div class="container">
        <div class="row align-items-center">
          <div class="col-md-8">
            <h1 class="mb-1">
              <i class="fab fa-vuejs me-2"></i>
              Article Management
            </h1>
            <p class="mb-0 opacity-75">Vue.js Frontend Interface</p>
          </div>
          <div class="col-md-4 text-end">
            <button class="btn btn-light btn-lg" @click="tambah">
              <i class="fas fa-plus me-2"></i>
              Tambah Artikel
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Enhanced Modal -->
    <div class="modal-overlay" v-if="showForm" @click.self="closeModal">
      <div class="modal-content-enhanced">
        <div class="modal-header-enhanced">
          <h4 class="modal-title-enhanced">
            <i class="fas fa-edit me-2"></i>
            {{ formTitle }}
          </h4>
          <button class="btn-close-enhanced" @click="closeModal">
            <i class="fas fa-times"></i>
          </button>
        </div>

        <form @submit.prevent="saveData" enctype="multipart/form-data" class="modal-form">
          <!-- Judul Artikel -->
          <div class="form-group-enhanced">
            <label class="form-label-enhanced">
              <i class="fas fa-heading me-2"></i>
              Judul Artikel
            </label>
            <input type="text"
                   v-model="formData.judul"
                   class="form-input-enhanced"
                   placeholder="Masukkan judul artikel yang menarik..."
                   required />
          </div>

          <!-- Rich Text Editor -->
          <div class="form-group-enhanced">
            <label class="form-label-enhanced">
              <i class="fas fa-edit me-2"></i>
              Isi Artikel
            </label>

            <!-- Enhanced Toolbar -->
            <div class="editor-toolbar-enhanced">
              <button type="button"
                      class="toolbar-btn"
                      @click="insertFormat('\n\n### ', '')"
                      title="Heading">
                <i class="fas fa-heading"></i>
                <span>Heading</span>
              </button>
              <button type="button"
                      class="toolbar-btn"
                      @click="insertFormat('\n- ', '')"
                      title="List">
                <i class="fas fa-list-ul"></i>
                <span>List</span>
              </button>
              <button type="button"
                      class="toolbar-btn"
                      @click="insertFormat('\n\n---\n\n', '')"
                      title="Separator">
                <i class="fas fa-minus"></i>
                <span>Line</span>
              </button>
            </div>

            <!-- Enhanced Editor Area -->
            <div class="editor-wrapper">
              <textarea
                ref="editorTextarea"
                v-model="formData.isi"
                class="form-textarea-enhanced"
                rows="8"
                placeholder="Tulis artikel Anda di sini...

Tips formatting:
• ### Judul untuk heading
• - Item untuk list
• Tekan Enter 2x untuk paragraf baru"
                required
              ></textarea>
            </div>

            <div class="editor-help">
              <i class="fas fa-info-circle me-2"></i>
              <small>Gunakan toolbar di atas untuk formatting cepat</small>
            </div>
          </div>

          <!-- Kategori -->
          <div class="form-group-enhanced">
            <label class="form-label-enhanced">
              <i class="fas fa-folder me-2"></i>
              Kategori Artikel
            </label>
            <select v-model="formData.id_kategori"
                    class="form-select-enhanced"
                    required>
              <option value="">-- Pilih Kategori --</option>
              <option v-for="kat in kategori"
                      :key="kat.id_kategori"
                      :value="kat.id_kategori">
                {{ kat.nama_kategori }}
              </option>
            </select>
          </div>

          <!-- Status -->
          <div class="form-group-enhanced">
            <label class="form-label-enhanced">
              <i class="fas fa-toggle-on me-2"></i>
              Status Publikasi
            </label>
            <select v-model="formData.status" class="form-select-enhanced">
              <option v-for="option in statusOptions"
                      :key="option.value"
                      :value="option.value">
                {{ option.text }}
              </option>
            </select>
          </div>

          <!-- Upload Gambar -->
          <div class="form-group-enhanced">
            <label class="form-label-enhanced">
              <i class="fas fa-image me-2"></i>
              Gambar Artikel
            </label>
            <div class="file-upload-wrapper">
              <input type="file"
                     id="gambar"
                     ref="fileInput"
                     @change="handleFileUpload"
                     accept="image/*"
                     class="file-input-hidden" />
              <label for="gambar" class="file-upload-btn">
                <i class="fas fa-cloud-upload-alt me-2"></i>
                Pilih Gambar
              </label>
              <div class="file-info">
                <i class="fas fa-info-circle me-1"></i>
                Format: JPG, PNG, GIF. Maksimal 2MB
              </div>
            </div>
          </div>

          <!-- Preview Gambar -->
          <div v-if="imagePreview" class="form-group-enhanced">
            <label class="form-label-enhanced">
              <i class="fas fa-eye me-2"></i>
              Preview Gambar
            </label>
            <div class="image-preview-wrapper">
              <img :src="imagePreview"
                   alt="Preview"
                   class="image-preview-enhanced" />
              <button type="button"
                      class="remove-image-btn"
                      @click="removeImage"
                      title="Hapus gambar">
                <i class="fas fa-times"></i>
              </button>
            </div>
          </div>

          <!-- Gambar Existing (saat edit) -->
          <div v-if="formData.gambar && !imagePreview" class="form-group-enhanced">
            <label class="form-label-enhanced">
              <i class="fas fa-image me-2"></i>
              Gambar Saat Ini
            </label>
            <div class="image-preview-wrapper">
              <img :src="'http://localhost/ci4/public/gambar/' + formData.gambar"
                   alt="Current Image"
                   class="image-preview-enhanced" />
              <button type="button"
                      class="remove-image-btn"
                      @click="removeExistingImage"
                      title="Hapus gambar">
                <i class="fas fa-trash"></i>
              </button>
            </div>
          </div>

          <!-- Hidden ID -->
          <input type="hidden" v-model="formData.id" />

          <!-- Submit Buttons -->
          <div class="form-actions-enhanced">
            <button type="submit"
                    class="btn-submit-enhanced"
                    :disabled="isUploading">
              <i class="fas fa-save me-2"></i>
              {{ isUploading ? 'Menyimpan...' : 'Simpan Artikel' }}
            </button>
            <button type="button"
                    class="btn-cancel-enhanced"
                    @click="closeModal">
              <i class="fas fa-times me-2"></i>
              Batal
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- Tabel Artikel dengan Scroll -->
    <div class="table-container">
      <table>
        <thead>
          <tr>
            <th>ID</th>
            <th>Gambar</th>
            <th>Judul</th>
            <th>Isi</th>
            <th>Kategori</th>
            <th>Status</th>
            <th>Aksi</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(row, index) in artikel" :key="row.id">
            <td class="center-text">{{ row.id }}</td>
            <td class="center-text">
              <img v-if="row.gambar"
                   :src="'http://localhost/ci4/public/gambar/' + row.gambar"
                   alt="Gambar Artikel"
                   class="table-image"
                 style="width: 25px !important; height: 25px !important; max-width: 25px !important; max-height: 25px !important; object-fit: cover !important;" />
              <span v-else class="no-image">No Image</span>
            </td>
            <td>{{ row.judul }}</td>
            <td class="isi-column">
              <div v-html="formatIsi(row.isi)"></div>
            </td>
            <td>{{ getNamaKategori(row.id_kategori) }}</td>
            <td>{{ statusText(row.status) }}</td>
            <td class="center-text">
              <a href="#" @click.prevent="edit(row)">Edit</a>
              <a href="#" @click.prevent="hapus(index, row.id)">Hapus</a>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>

  <!-- Hubungkan dengan Vue App -->
  <script src="assets/js/app.js"></script>
</body>
</html>
